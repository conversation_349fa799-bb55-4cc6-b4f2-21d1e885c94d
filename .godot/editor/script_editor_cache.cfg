[res://scripts/MobilePerformanceManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 43,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 203,
"scroll_position": 176.0,
"selection": true,
"selection_from_column": 0,
"selection_from_line": 203,
"selection_to_column": 43,
"selection_to_line": 203,
"syntax_highlighter": "GDScript"
}

[res://scripts/MobileInputHandler.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 60,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/UIScaler.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 119,
"scroll_position": 94.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
