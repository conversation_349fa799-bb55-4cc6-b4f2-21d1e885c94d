list=[{
"base": &"Control",
"class": &"ChapterCard",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ChapterCard.gd"
}, {
"base": &"Control",
"class": &"ChaptersCarousel",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ChaptersCarousel.gd"
}, {
"base": &"Node",
"class": &"FallbackAssetGenerator",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/FallbackAssetGenerator.gd"
}, {
"base": &"MobileMenuButton",
"class": &"MobileCarouselButton",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/MobileCarouselButton.gd"
}, {
"base": &"Button",
"class": &"MobileMenuButton",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/MobileMenuButton.gd"
}, {
"base": &"HTTPRequest",
"class": &"ScenarioAPI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ScenarioAPI.gd"
}]
