# 📱 Deployment Guide - Prekliate Dedičstvo

## Mobile Store Deployment Guide

### 🤖 Android Deployment

#### Príprava Projektu

1. **Godot Export Settings**
   ```
   Project -> Export -> Add -> Android
   ```

2. **Konfigurácia Android SDK**
   - Stiahnite Android Studio
   - Nastavte Android SDK path v Godot
   - Nainštalujte Android Build Tools

3. **Keystore Vytvorenie**
   ```bash
   keytool -genkey -v -keystore prekliate_dedicstvo.keystore -alias cursed_legacy -keyalg RSA -keysize 2048 -validity 10000
   ```

#### Export Nastavenia

**Basic Settings:**
- **Package Name:** `com.cursedlegacy.prekliatededicstvo`
- **Version Code:** 1
- **Version Name:** "1.0.0"
- **Min SDK:** 21 (Android 5.0)
- **Target SDK:** 33

**Permissions:**
- `android.permission.INTERNET` - Pre Scenario API
- `android.permission.ACCESS_NETWORK_STATE` - Network monitoring
- `android.permission.VIBRATE` - Haptic feedback

**Screen Settings:**
- **Orientation:** Portrait
- **Support Small Screens:** false
- **Support Normal Screens:** true
- **Support Large Screens:** true
- **Support XLarge Screens:** true

#### Google Play Store

1. **Developer Account**
   - Registrácia na Google Play Console
   - Platba $25 registračného poplatku

2. **App Bundle Vytvorenie**
   ```
   Godot -> Export -> Android (Release)
   -> Generate App Bundle (.aab)
   ```

3. **Store Listing**
   - **Title:** "Prekliate Dedičstvo"
   - **Short Description:** "Gothic adventure in 1894 Carpathians"
   - **Full Description:** [Detailný popis hry]
   - **Category:** Adventure
   - **Content Rating:** Teen (13+)

4. **Screenshots Requirements**
   - Phone: 1080x1920 (portrait)
   - Tablet: 2048x1536 (landscape)
   - Minimum 2 screenshots, maximum 8

5. **App Icon**
   - 512x512 PNG
   - High-res icon pre store listing

#### Release Process

1. **Internal Testing**
   ```
   Play Console -> Testing -> Internal testing
   Upload AAB -> Create release
   ```

2. **Closed Testing**
   - Alpha/Beta testing s limited users
   - Feedback collection a bug fixes

3. **Production Release**
   - Final review a approval
   - Staged rollout (5% -> 20% -> 50% -> 100%)

### 🍎 iOS Deployment

#### Príprava Projektu

1. **Xcode Setup**
   - Nainštalujte Xcode z App Store
   - Nastavte iOS SDK path v Godot

2. **Apple Developer Account**
   - Registrácia na developer.apple.com
   - Ročný poplatok $99

3. **Certificates & Provisioning**
   ```
   Xcode -> Preferences -> Accounts
   Add Apple ID -> Download Manual Profiles
   ```

#### Export Nastavenia

**Basic Settings:**
- **Bundle Identifier:** `com.cursedlegacy.prekliatededicstvo`
- **Version:** "1.0"
- **Short Version:** "1.0.0"
- **Target Device:** iPhone/iPad
- **Deployment Target:** iOS 12.0

**Capabilities:**
- Network requests pre Scenario API
- Haptic feedback support

#### App Store Connect

1. **App Creation**
   ```
   App Store Connect -> My Apps -> + -> New App
   ```

2. **App Information**
   - **Name:** "Prekliate Dedičstvo"
   - **Subtitle:** "Cursed Legacy"
   - **Category:** Games/Adventure
   - **Age Rating:** 12+ (Infrequent/Mild Horror/Fear Themes)

3. **Screenshots Requirements**
   - iPhone: 1290x2796 (iPhone 14 Pro Max)
   - iPad: 2048x2732 (12.9" iPad Pro)
   - Minimum 1 screenshot per device type

4. **App Review Information**
   - Demo account credentials (if needed)
   - Review notes explaining Scenario API usage
   - Contact information

#### Release Process

1. **Archive Build**
   ```
   Xcode -> Product -> Archive
   Organizer -> Distribute App -> App Store Connect
   ```

2. **TestFlight Beta**
   - Internal testing (up to 100 testers)
   - External testing (up to 10,000 testers)
   - Beta App Review required

3. **App Store Review**
   - Submit for review
   - Review time: 24-48 hours typically
   - Address any rejection feedback

### 🔧 Technical Requirements

#### Performance Benchmarks

**Minimum Requirements:**
- **RAM:** 2GB
- **Storage:** 100MB initial + 50MB cache
- **CPU:** Quad-core 1.5GHz
- **GPU:** Adreno 530 / Mali-G71 MP8 equivalent

**Recommended:**
- **RAM:** 4GB+
- **Storage:** 200MB available
- **CPU:** Octa-core 2.0GHz+
- **GPU:** Adreno 640 / Mali-G76 MP12+

#### Network Requirements

- **Internet Connection:** Required for asset generation
- **Bandwidth:** Minimum 1Mbps for API calls
- **Offline Mode:** Basic functionality with cached assets
- **Data Usage:** ~5-10MB per session (asset generation)

### 📋 Pre-Launch Checklist

#### Functionality Testing
- [ ] All menu buttons functional
- [ ] Asset generation working
- [ ] Fallback assets loading
- [ ] Performance optimization active
- [ ] Battery optimization working
- [ ] Safe area handling correct
- [ ] Touch targets minimum 44dp
- [ ] Haptic feedback working

#### Platform Testing
- [ ] Android 5.0+ compatibility
- [ ] iOS 12.0+ compatibility
- [ ] Various screen sizes tested
- [ ] Portrait orientation locked
- [ ] Notch/safe area handling
- [ ] Hardware back button (Android)

#### Store Requirements
- [ ] Privacy policy created
- [ ] Terms of service written
- [ ] Age rating appropriate
- [ ] Screenshots captured
- [ ] App icon finalized
- [ ] Store descriptions written
- [ ] Keywords researched

#### Legal & Compliance
- [ ] GDPR compliance (EU users)
- [ ] COPPA compliance (under 13)
- [ ] Data collection disclosure
- [ ] Third-party licenses included
- [ ] Scenario API terms compliance

### 🚀 Launch Strategy

#### Soft Launch
1. **Limited Geographic Release**
   - Start with 1-2 countries
   - Monitor performance and feedback
   - Fix critical issues

2. **Gradual Rollout**
   - Expand to more regions
   - Monitor server load (Scenario API)
   - Scale infrastructure as needed

#### Marketing Assets
- **App Store Optimization (ASO)**
  - Keywords: gothic, adventure, mobile game
  - Compelling screenshots
  - Engaging app preview video

- **Social Media**
  - Development behind-the-scenes
  - Gothic art showcase
  - Community building

### 📊 Post-Launch Monitoring

#### Analytics Setup
- **Crash Reporting:** Integrate crash analytics
- **Performance Monitoring:** FPS, memory usage
- **User Behavior:** Menu navigation patterns
- **API Usage:** Scenario API success rates

#### Key Metrics
- **Retention Rates:** Day 1, 7, 30
- **Session Length:** Average time in app
- **Crash Rate:** < 1% target
- **Performance:** 60 FPS on target devices
- **API Success:** > 95% generation success

#### Update Strategy
- **Hotfixes:** Critical bugs within 24 hours
- **Minor Updates:** Monthly feature additions
- **Major Updates:** Quarterly content releases
- **Asset Updates:** New gothic themes seasonally

### 🔒 Security & Privacy

#### Data Protection
- **Local Storage:** Only cached assets and settings
- **Network Traffic:** HTTPS only for API calls
- **User Privacy:** No personal data collection
- **Analytics:** Anonymized usage data only

#### API Security
- **Key Management:** Secure storage of API credentials
- **Rate Limiting:** Respect Scenario API limits
- **Error Handling:** Graceful API failure handling
- **Offline Fallback:** Cached assets for offline use

---

**Deployment Checklist Completion:** ✅ Ready for Store Submission  
**Estimated Review Time:** Android: 1-3 days, iOS: 1-2 days  
**Launch Target:** Q3 2025
