# 🏰 Gothic Main Menu - Deployment Guide

## 📋 Overview

Kompletný gothic mobilný main menu systém pre "Prekliate Dedičstvo" s AI-generovan<PERSON>mi assetmi, mobilnými optimalizáciami a <PERSON> estetikou.

## 🎨 Features

### ✅ Gothic Art Style
- **Hand-drawn illustration** s <PERSON> estetikou
- **Konzistentná farebná paleta:** tmavé tóny s zlatými akcentmi
- **Atmosférické efekty:** hmla, blesky, havran animácie
- **Scenario API integrácia** pre real-time asset generovanie

### ✅ Mobile Optimizations
- **Portrait orientácia** (1080x1920)
- **Touch-friendly UI** (min 44dp touch targets)
- **Responsive scaling** pre všetky screen sizes
- **Performance optimalizácie** pre mobile GPU/CPU
- **Safe area support** pre notches/bezels

### ✅ Asset Management
- **AI-generované assety** cez Scenario API
- **Fallback systém** pre offline použitie
- **Smart caching** s progressive loading
- **Memory optimalizácie** (< 150MB RAM)

## 🚀 Quick Start

### 1. Spustenie v Godot
```bash
# Otvorte projekt v Godot 4.2+
cd /Users/<USER>/Desktop/GAME_1

# Spustite MainMenu.tscn scénu
# Alebo TestGothicMenu.tscn pre testing
```

### 2. Generovanie Assets
```gdscript
# V Godot editore spustite:
# Tools > Execute Script > generate_fallback_assets.gd

# Alebo programaticky:
AssetManager.generate_main_menu_assets()
```

### 3. Testing
```bash
# Spustite Python test suite
python3 test_gothic_main_menu.py

# Alebo použite test scénu
# Spustite scenes/TestGothicMenu.tscn
```

## 📱 Mobile Deployment

### Android Export
1. **Nastavte Android SDK** v Godot Project Settings
2. **Konfigurácia export_presets.cfg:**
   - Min SDK: 21 (Android 5.0+)
   - Target SDK: 33
   - Architecture: arm64-v8a
   - Screen orientation: Portrait
   - Immersive mode: true

3. **Build APK:**
   ```bash
   # V Godot editore:
   # Project > Export > Android
   ```

### iOS Export
1. **Nastavte Xcode** a iOS SDK
2. **Konfigurácia:**
   - Target device: iPhone/iPad
   - iOS version: 12.0+
   - Bundle identifier: com.cursedlegacy.prekliatededicstvo

3. **Build IPA:**
   ```bash
   # V Godot editore:
   # Project > Export > iOS
   ```

## 🎮 Asset Specifications

### Main Menu Assets
| Asset Type | Dimensions | Priority | Description |
|------------|------------|----------|-------------|
| main_background | 1080x1920 | 1 | Gothic castle silhouette |
| menu_frame | 800x1200 | 2 | Ornate stone frame |
| button_normal | 400x120 | 3 | Weathered stone button |
| button_pressed | 400x120 | 3 | Glowing pressed state |
| button_hover | 400x120 | 3 | Blue mystical glow |
| title_ornament | 600x200 | 2 | Decorative border |
| loading_spinner | 128x128 | 4 | Runic circle |

### Gothic Art Prompts
```gdscript
const ART_STYLE_BASE = "hand-drawn illustration, gothic cartoon style, Tim Burton aesthetic, dark fairy tale art, sketch-like lineart, atmospheric lighting, mobile game art"

# Príklad pre button:
"hand-drawn illustration, gothic cartoon style, Tim Burton aesthetic, weathered stone button with carved gothic runes, moss and age details, rectangular medieval stone tablet, mobile UI element design"
```

## ⚡ Performance Targets

### Mobile Performance
- **FPS:** 60 na high-end, 30 na low-end zariadenia
- **Memory:** < 150MB RAM pre všetky assety
- **Startup:** < 3 sekundy s cached assetmi
- **Touch Response:** < 50ms latency
- **Asset Generation:** 5-15 sekúnd per asset

### Optimalizácie
- **Texture compression** pre memory efficiency
- **Progressive loading** s fallback assetmi
- **Particle system optimization** pre mobile
- **Battery-aware performance scaling**

## 🔧 Configuration

### Scenario API Setup
```gdscript
# V scripts/ScenarioAPI.gd
const API_KEY = "api_StCDGknMcAZP7RKggd5rnfVE"
const API_SECRET = "rJUJf3coKs1cbcxAxNWQ8ynf"
const API_BASE_URL = "https://api.cloud.scenario.com/v1"

# Mobile-optimized settings
const MOBILE_GENERATION_SETTINGS = {
    "guidance": 3.5,
    "steps": 20,
    "scheduler": "EulerAncestralDiscreteScheduler",
    "modelId": "flux.1-dev"
}
```

### Project Settings
```ini
# project.godot
[application]
config/name="Prekliate Dedičstvo"
run/main_scene="res://scenes/MainMenu.tscn"
config/features=PackedStringArray("4.4", "Mobile")

[display]
window/size/viewport_width=1080
window/size/viewport_height=1920
window/handheld/orientation=1

[rendering]
renderer/rendering_method="mobile"
textures/canvas_textures/default_texture_filter=2
```

## 🧪 Testing

### Test Suite
```bash
# Kompletný test
python3 test_gothic_main_menu.py

# Jednotlivé testy
python3 -c "
from test_gothic_main_menu import *
test_scenario_api_connection()
test_gothic_asset_generation()
test_mobile_optimizations()
"
```

### Interactive Testing
```gdscript
# V Godot spustite TestGothicMenu.tscn
# Klávesy:
# F1 - Generate main menu assets
# F2 - Generate fallbacks
# F3 - Test performance
# F4 - Test touch targets
# F5 - Toggle test UI
```

## 📊 Monitoring

### Performance Metrics
```gdscript
# Získanie štatistík
var stats = MobilePerformanceManager.get_performance_stats()
print("FPS: ", stats.fps)
print("Memory: ", stats.memory_usage_mb, " MB")

# Asset cache info
var cache_size = AssetManager.get_cache_size()
var is_cached = AssetManager.is_asset_cached("main_background")
```

### Debug Output
```gdscript
# Zapnite debug output v MainMenu.gd
print("Gothic asset generated: ", asset_type)
print("Lightning flash!")
print("Applied main background")
```

## 🔄 Fallback System

### Offline Assets
```bash
# Generovanie fallback assetov
# V Godot editore spustite generate_fallback_assets.gd

# Alebo programaticky:
FallbackAssetGenerator.generate_all_fallback_assets()
```

### Fallback Priority
1. **Cached AI assets** (user://generated_assets/)
2. **Fallback assets** (res://fallback_assets/)
3. **Procedural placeholders** (runtime generated)

## 🎯 Next Steps

### Immediate
1. **Test na mobile zariadení**
2. **Optimize asset generation times**
3. **Add more atmospheric effects**
4. **Implement save system**

### Future Enhancements
1. **Multiplayer menu support**
2. **Achievement gallery**
3. **Settings menu**
4. **Localization support**
5. **Advanced particle effects**

## 📞 Support

### Troubleshooting
- **API errors:** Check internet connection a API keys
- **Performance issues:** Reduce particle count, enable battery optimization
- **Touch problems:** Verify minimum touch target sizes (44dp)
- **Asset loading:** Check fallback assets exist

### Resources
- **Scenario API Docs:** https://docs.scenario.com
- **Godot Mobile Guide:** https://docs.godotengine.org/en/stable/tutorials/platform/mobile.html
- **Gothic Art Reference:** Tim Burton films, Don't Starve, Hollow Knight

---

**Verzia:** 1.0.0 Gothic Edition  
**Posledná aktualizácia:** 30.6.2025  
**Godot Verzia:** 4.2+  
**Licencia:** MIT
