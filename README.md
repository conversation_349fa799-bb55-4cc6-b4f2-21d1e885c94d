# Prekliate Dedičstvo (Cursed Legacy)
## Gothic Mobile Game - Main Menu System

### 🎮 Popis Projektu

"Prekliate Dedičstvo" je gotická interaktívna mobilná hra odohrajúca sa v roku 1894 v Karpatoch. <PERSON>r<PERSON><PERSON> hrá za ž<PERSON><PERSON>, ktorý musí zachrániť svojho mentora pred grófkou Isabelle Báthoryovou.

Tento projekt obsahuje kompletný mobilný main menu systém s integráciou Scenario API pre generovanie gotických assetov v kreslenenom štýle.

### 🏗️ Architektúra Projektu

```
res://
├── scenes/
│   ├── MainMenu.tscn              # Hlavná menu scéna
│   └── ui/
│       └── MobileMenuButton.tscn  # Mobilný button komponent
├── scripts/
│   ├── MainMenu.gd                # Hlavný menu controller
│   ├── ScenarioAPI.gd             # Scenario API integrácia
│   ├── AssetManager.gd            # Správa assetov a cache
│   ├── MobileMenuButton.gd        # Mobilný button komponent
│   ├── MobileInputHandler.gd      # Touch input handling
│   ├── UIScaler.gd                # Responsive UI scaling
│   ├── MobilePerformanceManager.gd # Performance optimalizácie
│   └── BatteryOptimizer.gd        # Batéria optimalizácie
├── generated_assets/              # AI generované assety
├── fallback_assets/               # Fallback placeholder assety
└── fonts/                         # Gothic fonty
```

### 🎨 Art Style & Assets

**Štýl:** Hand-drawn 2D illustration, gothic cartoon style
- **Inšpirácia:** Tim Burton aesthetic, Don't Starve art style
- **Farebná paleta:** 
  - Primárne: #1a1a1a (deep black), #8B4513 (dark brown), #2F4F4F (dark slate)
  - Akcentové: #D4AF37 (antique gold), #8B0000 (dark red), #483D8B (dark slate blue)

**Generované Assets:**
- `main_background` - Gotický hrad s búrkovým nebom (1080x1920)
- `menu_frame` - Ornamentálny gotický rám (800x1200)
- `button_normal/pressed` - Gotické kamenné tlačidlá (400x120)
- `title_ornament` - Dekoratívne ozdoby (600x200)
- `loading_spinner` - Mystický symbol (128x128)
- `particle_fog` - Hmla efekty (64x64)
- `raven_silhouette` - Havran silueta (128x128)

### 📱 Mobilné Optimalizácie

#### Performance Management
- **Automatická detekcia zariadenia** a nastavenie performance módu
- **Dynamické škálovanie** particle efektov podľa výkonu
- **Memory management** s automatickým garbage collection
- **FPS monitoring** a adaptívne nastavenia

#### Battery Optimization
- **Automatická detekcia** nízkej batérie
- **Power save mode** s redukciou FPS a efektov
- **Critical mode** pre kriticky nízku batériu
- **Adaptívne nastavenia** audio a vizuálnych efektov

#### Responsive Design
- **Safe area support** pre zariadenia s notch
- **Automatic scaling** pre rôzne rozlíšenia
- **Minimum touch targets** (44dp) pre accessibility
- **Portrait orientation** optimalizácia

### 🔧 Scenario API Integrácia

#### Konfigurácia
```gdscript
const API_KEY = "api_StCDGknMcAZP7RKggd5rnfVE"
const API_SECRET = "rJUJf3coKs1cbcxAxNWQ8ynf"
const MODEL_ID = "flux.1-dev"
```

#### Asset Generation Pipeline
1. **Queue Management** - Prioritizované generovanie assetov
2. **Job Polling** - Asynchrónne sledovanie progress
3. **Cache System** - Lokálne ukladanie generovaných assetov
4. **Fallback System** - Placeholder assety pre offline použitie

#### Mobile Optimizations
- **Compressed textures** pre memory efficiency
- **Progressive loading** s progress indikátormi
- **Error handling** s graceful fallbacks
- **Network optimization** pre mobile connections

### 🎯 Použitie

#### Spustenie Projektu
1. Otvorte projekt v Godot 4.2+
2. Nastavte export presets pre Android/iOS
3. Spustite `MainMenu.tscn` scénu

#### Generovanie Assets
```gdscript
# Automatické generovanie všetkých assetov
AssetManager.generate_all_mobile_assets()

# Generovanie konkrétneho assetu
AssetManager.generate_asset("main_background")

# Kontrola cache
if AssetManager.is_asset_cached("button_normal"):
    var texture = AssetManager.get_asset("button_normal")
```

#### Performance Monitoring
```gdscript
# Získanie performance štatistík
var stats = MobilePerformanceManager.get_performance_stats()
print("FPS: ", stats.fps)
print("Memory: ", stats.memory_usage_mb, " MB")

# Manuálne nastavenie performance módu
MobilePerformanceManager.set_performance_mode(PerformanceMode.LOW)
```

### 📋 Menu Funkcionalita

#### Tlačidlá Menu
- **Nová Hra** - Spustenie novej hry
- **Pokračovať** - Načítanie uloženej hry (disabled ak save neexistuje)
- **Nastavenia** - Otvorenie nastavení
- **Galéria** - Zobrazenie achievementov/galérie
- **Ukončiť** - Ukončenie aplikácie

#### Atmosférické Efekty
- **Fog Particles** - Hmla efekty optimalizované pre mobile
- **Lightning Flash** - Náhodné blesky každých 10-30 sekúnd
- **Raven Animation** - Animovaný havran s bobbing efektom
- **Dynamic Background** - AI generované pozadie s gradientom

### 🔧 Deployment

#### Android Export
1. Nastavte Android SDK v Godot
2. Konfigurácia v `export_presets.cfg`
3. Podpísanie APK s keystore
4. Upload na Google Play Store

#### iOS Export
1. Nastavte Xcode a iOS SDK
2. Konfigurácia provisioning profiles
3. Build a archive v Xcode
4. Upload na App Store Connect

#### Požiadavky
- **Android:** API level 21+ (Android 5.0+)
- **iOS:** iOS 12.0+
- **RAM:** Minimum 2GB odporúčané
- **Storage:** 100MB pre aplikáciu + cache

### 🧪 Testovanie

#### Performance Testing
```gdscript
# Test rôznych performance módov
for mode in PerformanceMode.values():
    MobilePerformanceManager.set_performance_mode(mode)
    await get_tree().create_timer(5.0).timeout
    var stats = MobilePerformanceManager.get_performance_stats()
    print("Mode: ", mode, " FPS: ", stats.fps)
```

#### Asset Generation Testing
```gdscript
# Test generovania všetkých assetov
AssetManager.generate_all_mobile_assets()
await AssetManager.all_assets_loaded
print("All assets generated successfully!")
```

#### Battery Testing
```gdscript
# Simulácia nízkej batérie
BatteryOptimizer.battery_level = 0.15
BatteryOptimizer._check_battery_status()
# Skontroluje či sa aktivoval power save mode
```

### 📊 Monitoring & Analytics

#### Performance Metrics
- FPS monitoring s automatickými adjustmentmi
- Memory usage tracking
- Battery level monitoring
- Network request success rates

#### User Experience
- Touch response times
- Asset loading times
- Menu navigation flows
- Error rates a fallback usage

### 🔮 Budúce Rozšírenia

#### Plánované Features
- **Multiplayer menu** - Online lobby systém
- **Achievement system** - Unlock system s progress tracking
- **Settings menu** - Audio, graphics, controls nastavenia
- **Save system** - Cloud save synchronizácia
- **Localization** - Podpora viacerých jazykov

#### Technical Improvements
- **Advanced caching** - Intelligent asset preloading
- **Network optimization** - Better offline handling
- **Performance profiling** - Detailed analytics
- **Accessibility** - Screen reader support, colorblind options

### 📞 Support & Kontakt

Pre technické otázky a support:
- **GitHub Issues** - Bug reports a feature requests
- **Documentation** - Detailná dokumentácia v `/docs`
- **Community** - Discord server pre developers

---

**Verzia:** 1.0.0  
**Posledná aktualizácia:** 30.6.2025  
**Godot Verzia:** 4.2+  
**Licencia:** MIT
