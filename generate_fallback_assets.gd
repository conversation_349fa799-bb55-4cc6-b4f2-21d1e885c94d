@tool
extends EditorScript

# This script generates fallback assets for the game
# Run this in Godot Editor: Tools -> Execute Script

func _run():
	print("Starting fallback asset generation...")
	
	# Ensure fallback_assets directory exists
	var dir = DirAccess.open("res://")
	if not dir.dir_exists("fallback_assets"):
		dir.make_dir("fallback_assets")
		print("Created fallback_assets directory")
	
	generate_main_background()
	generate_button_normal()
	generate_button_pressed()
	generate_menu_frame()
	generate_title_ornament()
	generate_loading_spinner()
	generate_particle_fog()
	generate_raven_silhouette()
	generate_default_assets()
	
	print("Fallback asset generation completed!")

func generate_main_background():
	var image = Image.create(1080, 1920, false, Image.FORMAT_RGBA8)
	
	# Create gothic gradient background
	for y in range(1920):
		for x in range(1080):
			var gradient_factor = float(y) / 1920.0
			var base_color = Color(0.05, 0.05, 0.1, 1.0)
			var top_color = Color(0.1, 0.08, 0.15, 1.0)
			
			var color = base_color.lerp(top_color, 1.0 - gradient_factor)
			
			# Add castle silhouette effect
			if y > 1200:
				var castle_factor = sin(float(x) / 100.0) * 0.3 + 0.7
				if y > 1200 + (castle_factor * 200):
					color = Color(0.02, 0.02, 0.05, 1.0)
			
			image.set_pixel(x, y, color)
	
	save_image(image, "main_background")

func generate_button_normal():
	var image = create_gothic_button(400, 120, false)
	save_image(image, "button_normal")

func generate_button_pressed():
	var image = create_gothic_button(400, 120, true)
	save_image(image, "button_pressed")

func create_gothic_button(width: int, height: int, pressed: bool) -> Image:
	var image = Image.create(width, height, false, Image.FORMAT_RGBA8)
	
	var base_color = Color(0.3, 0.25, 0.2, 0.9) if not pressed else Color(0.25, 0.2, 0.15, 0.9)
	var border_color = Color(0.6, 0.5, 0.3, 1.0)
	var highlight_color = Color(0.4, 0.35, 0.25, 1.0)
	
	# Fill background
	image.fill(base_color)
	
	# Draw border
	for x in range(width):
		for y in range(height):
			if x < 3 or x >= width - 3 or y < 3 or y >= height - 3:
				image.set_pixel(x, y, border_color)
			elif x < 6 or x >= width - 6 or y < 6 or y >= height - 6:
				image.set_pixel(x, y, highlight_color)
	
	return image

func generate_menu_frame():
	var image = Image.create(800, 1200, false, Image.FORMAT_RGBA8)
	
	var frame_color = Color(0.4, 0.3, 0.2, 0.8)
	var border_color = Color(0.6, 0.5, 0.3, 1.0)
	var inner_color = Color(0.0, 0.0, 0.0, 0.0)
	
	var frame_thickness = 40
	
	image.fill(inner_color)
	
	# Draw frame
	for x in range(800):
		for y in range(1200):
			if x < frame_thickness or x >= 800 - frame_thickness or y < frame_thickness or y >= 1200 - frame_thickness:
				if x < 5 or x >= 800 - 5 or y < 5 or y >= 1200 - 5:
					image.set_pixel(x, y, border_color)
				else:
					image.set_pixel(x, y, frame_color)
	
	save_image(image, "menu_frame")

func generate_title_ornament():
	var image = Image.create(600, 200, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	var color = Color(0.8, 0.7, 0.4, 0.8)
	var center_y = 100
	
	# Draw decorative lines
	for x in range(50, 550):
		var y_offset = sin(float(x) / 50.0) * 10
		var y = center_y + int(y_offset)
		
		if y >= 0 and y < 200:
			image.set_pixel(x, y, color)
			if y > 0:
				image.set_pixel(x, y - 1, Color(color.r, color.g, color.b, color.a * 0.5))
			if y < 199:
				image.set_pixel(x, y + 1, Color(color.r, color.g, color.b, color.a * 0.5))
	
	save_image(image, "title_ornament")

func generate_loading_spinner():
	var image = Image.create(128, 128, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	var center = Vector2(64, 64)
	var radius = 50
	var color = Color(0.8, 0.7, 0.5, 1.0)
	
	# Draw spinning circle segments
	for angle in range(0, 360, 10):
		var rad = deg_to_rad(angle)
		var x = center.x + cos(rad) * radius
		var y = center.y + sin(rad) * radius
		
		var alpha = float(angle) / 360.0
		var segment_color = Color(color.r, color.g, color.b, alpha)
		
		# Draw small circle at this position
		for dx in range(-3, 4):
			for dy in range(-3, 4):
				if dx * dx + dy * dy <= 9:
					var px = int(x + dx)
					var py = int(y + dy)
					if px >= 0 and px < 128 and py >= 0 and py < 128:
						image.set_pixel(px, py, segment_color)
	
	save_image(image, "loading_spinner")

func generate_particle_fog():
	var image = Image.create(64, 64, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	var center = Vector2(32, 32)
	var max_radius = 32
	
	for x in range(64):
		for y in range(64):
			var distance = Vector2(x, y).distance_to(center)
			if distance <= max_radius:
				var alpha = 1.0 - (distance / max_radius)
				alpha = alpha * alpha
				var color = Color(0.9, 0.9, 1.0, alpha * 0.3)
				image.set_pixel(x, y, color)
	
	save_image(image, "particle_fog")

func generate_raven_silhouette():
	var image = Image.create(128, 128, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	var color = Color(0.1, 0.1, 0.1, 1.0)
	var center_x = 64
	var center_y = 64
	
	# Simple raven shape
	# Body
	for y in range(center_y - 10, center_y + 20):
		for x in range(center_x - 8, center_x + 8):
			if x >= 0 and x < 128 and y >= 0 and y < 128:
				image.set_pixel(x, y, color)
	
	# Head
	for y in range(center_y - 20, center_y - 5):
		for x in range(center_x - 6, center_x + 6):
			if x >= 0 and x < 128 and y >= 0 and y < 128:
				var distance = Vector2(x - center_x, y - (center_y - 12)).length()
				if distance <= 8:
					image.set_pixel(x, y, color)
	
	# Wings
	for y in range(center_y - 5, center_y + 10):
		for x in range(center_x - 25, center_x - 8):
			if x >= 0 and x < 128 and y >= 0 and y < 128:
				image.set_pixel(x, y, color)
		for x in range(center_x + 8, center_x + 25):
			if x >= 0 and x < 128 and y >= 0 and y < 128:
				image.set_pixel(x, y, color)
	
	save_image(image, "raven_silhouette")

func generate_default_assets():
	# Default background
	var bg_image = Image.create(1080, 1920, false, Image.FORMAT_RGBA8)
	bg_image.fill(Color(0.1, 0.1, 0.15, 1.0))
	save_image(bg_image, "default_bg")
	
	# Default button
	var btn_image = create_gothic_button(400, 120, false)
	save_image(btn_image, "default_button")
	
	# Loading icon
	var icon_image = Image.create(64, 64, false, Image.FORMAT_RGBA8)
	icon_image.fill(Color(0.5, 0.5, 0.5, 1.0))
	save_image(icon_image, "loading_icon")

func save_image(image: Image, name: String):
	var path = "res://fallback_assets/" + name + ".png"
	var error = image.save_png(path)
	if error == OK:
		print("Saved: ", path)
	else:
		print("Failed to save: ", name, " - Error: ", error)
