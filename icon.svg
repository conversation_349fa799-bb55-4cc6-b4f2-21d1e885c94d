<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="bg" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#2F4F4F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="128" height="128" fill="url(#bg)" rx="16"/>
  
  <!-- Castle silhouette -->
  <path d="M20 100 L30 85 L35 90 L40 80 L45 85 L50 75 L55 80 L60 70 L65 75 L70 65 L75 70 L80 60 L85 65 L90 55 L95 60 L100 50 L108 55 L108 110 L20 110 Z" fill="#0a0a0a"/>
  
  <!-- Moon -->
  <circle cx="90" cy="25" r="15" fill="#D4AF37" opacity="0.8"/>
  
  <!-- Gothic ornament -->
  <path d="M64 45 L54 55 L64 65 L74 55 Z" fill="#8B0000" opacity="0.9"/>
  <path d="M64 40 L59 50 L64 60 L69 50 Z" fill="#D4AF37" opacity="0.7"/>
  
  <!-- Title initial -->
  <text x="64" y="85" font-family="serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#D4AF37">P</text>
</svg>
