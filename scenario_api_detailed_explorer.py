#!/usr/bin/env python3
"""
Detailný prieskum Scenario API - testovanie všetkých funkcií pre herný vývoj
"""

import requests
import time
import base64
import json

# API kľúče
API_KEY = "api_StCDGknMcAZP7RKggd5rnfVE"
API_SECRET = "rJUJf3coKs1cbcxAxNWQ8ynf"
BASE_URL = "https://api.cloud.scenario.com/v1"

def get_auth_header():
    credentials = base64.b64encode(f"{API_KEY}:{API_SECRET}".encode()).decode()
    return {"Authorization": f"Basic {credentials}"}

def explore_public_models():
    """Preskúmanie verejných modelov"""
    print("\n🌐 Preskúmanie verejných modelov...")
    
    headers = get_auth_header()
    headers["accept"] = "application/json"
    
    try:
        # Skús získať verejné modely
        response = requests.get(f"{BASE_URL}/assets/public", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Verejné assety: {len(data.get('assets', []))}")
        else:
            print(f"ℹ️ Verejné assety nedostupné: {response.status_code}")
            
        # Skús získať odporúčané modely
        response = requests.get(f"{BASE_URL}/recommendations/models", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"✅ Odporúčané modely: {len(models)}")
            
            for model in models[:5]:
                print(f"  📦 {model.get('name', 'N/A')} - {model.get('id', 'N/A')}")
                print(f"     Kategória: {model.get('category', 'N/A')}")
                
        else:
            print(f"ℹ️ Odporúčané modely nedostupné: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Chyba: {e}")

def test_flux_model():
    """Test s verejným Flux modelom"""
    print("\n⚡ Test s Flux modelom...")
    
    headers = get_auth_header()
    headers["Content-Type"] = "application/json"
    
    # Herný prompt pre fantasy hru
    game_prompts = [
        "medieval fantasy knight in armor, game character design, detailed",
        "magical forest background, game environment art, atmospheric",
        "fantasy sword weapon, game item, detailed texture",
        "dragon creature, game monster design, fierce"
    ]
    
    for i, prompt in enumerate(game_prompts[:2]):  # Test len 2 prompty
        print(f"\n🎨 Generovanie: {prompt}")
        
        payload = {
            "modelId": "flux.1-dev",
            "prompt": prompt,
            "numSamples": 1,
            "guidance": 3.5,
            "numInferenceSteps": 20,
            "width": 512,
            "height": 512
        }
        
        try:
            response = requests.post(f"{BASE_URL}/generate/txt2img", 
                                   headers=headers, json=payload)
            
            if response.status_code == 200:
                data = response.json()
                job_id = data.get('job', {}).get('jobId')
                print(f"✅ Job {i+1} vytvorený: {job_id}")
            else:
                print(f"❌ Chyba job {i+1}: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Chyba: {e}")

def explore_3d_capabilities():
    """Preskúmanie 3D možností"""
    print("\n🗿 Preskúmanie 3D možností...")
    
    # Zoznam 3D modelov z dokumentácie
    model_3d_ids = [
        "model_hunyuan-3d-v2-1",
        "model_instant-mesh",
        "model_triposr"
    ]
    
    print("📋 Dostupné 3D modely:")
    for model_id in model_3d_ids:
        print(f"  🎯 {model_id}")
    
    print("\n💡 3D generovanie vyžaduje:")
    print("  - Vstupný obrázok (asset ID)")
    print("  - Parametre ako steps, guidanceScale")
    print("  - Dlhší čas spracovania (async job)")

def explore_video_capabilities():
    """Preskúmanie video možností"""
    print("\n🎬 Preskúmanie video možností...")
    
    # Video modely z dokumentácie
    video_models = [
        "model_runway-gen3-alpha-turbo",
        "model_luma-dream-machine-v1.5",
        "model_kling-v1.5"
    ]
    
    print("📋 Dostupné video modely:")
    for model_id in video_models:
        print(f"  🎥 {model_id}")
    
    print("\n💡 Video generovanie podporuje:")
    print("  - Text-to-Video")
    print("  - Image-to-Video") 
    print("  - Rôzne rozlíšenia a dĺžky")

def explore_game_specific_features():
    """Preskúmanie funkcií špecifických pre hry"""
    print("\n🎮 Funkcie užitočné pre herný vývoj:")
    
    game_features = {
        "🏰 Environment Art": [
            "Skybox generovanie pre pozadia",
            "Textúry pre terén a objekty",
            "Atmospheric efekty"
        ],
        "👤 Character Design": [
            "Koncepty postáv",
            "Variácie outfitov",
            "Facial expressions"
        ],
        "⚔️ Game Assets": [
            "Zbrane a nástroje",
            "UI elementy",
            "Ikony a symboly"
        ],
        "🌍 World Building": [
            "Mapy a layouty",
            "Architektúra",
            "Props a dekorácie"
        ]
    }
    
    for category, features in game_features.items():
        print(f"\n{category}:")
        for feature in features:
            print(f"  • {feature}")

def test_image_editing():
    """Test editovacích funkcií"""
    print("\n✂️ Test editovacích funkcií...")
    
    editing_endpoints = [
        "/generate/remove-background",
        "/generate/upscale", 
        "/generate/vectorize",
        "/generate/inpaint",
        "/generate/detect"
    ]
    
    print("🛠️ Dostupné editovacie funkcie:")
    for endpoint in editing_endpoints:
        print(f"  📝 {endpoint}")
    
    print("\n💡 Tieto funkcie umožňujú:")
    print("  - Odstránenie pozadia z obrázkov")
    print("  - Zväčšenie rozlíšenia")
    print("  - Vektorizáciu bitmap")
    print("  - Inpainting/opravy")
    print("  - Detekciu objektov")

def check_usage_and_limits():
    """Kontrola využitia a limitov"""
    print("\n📊 Kontrola využitia API...")
    
    headers = get_auth_header()
    
    try:
        response = requests.get(f"{BASE_URL}/usages", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Informácie o využití:")
            print(f"  📈 Dáta: {json.dumps(data, indent=2)}")
        else:
            print(f"ℹ️ Využitie nedostupné: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Chyba: {e}")

def create_game_development_plan():
    """Vytvorenie plánu pre herný vývoj"""
    print("\n📋 PLÁN VYUŽITIA SCENARIO API PRE HERNÝ VÝVOJ")
    print("=" * 60)
    
    phases = {
        "🎨 Fáza 1 - Koncept Art": [
            "Generovanie konceptov postáv",
            "Návrhy prostredí a levelov", 
            "Mood boards a štýlové referencie"
        ],
        "🏗️ Fáza 2 - Asset Production": [
            "Textúry pre 3D modely",
            "UI elementy a ikony",
            "Particle efekty a pozadia"
        ],
        "🎬 Fáza 3 - Marketing": [
            "Promotional artwork",
            "Trailer backgrounds",
            "Social media content"
        ],
        "🔄 Fáza 4 - Iterácia": [
            "A/B testing rôznych štýlov",
            "Seasonal content",
            "DLC artwork"
        ]
    }
    
    for phase, tasks in phases.items():
        print(f"\n{phase}:")
        for task in tasks:
            print(f"  ✓ {task}")

def main():
    """Hlavná funkcia"""
    print("🎮 SCENARIO API - DETAILNÝ PRIESKUM PRE HERNÝ VÝVOJ")
    print("=" * 70)
    
    # Základné testy
    explore_public_models()
    explore_3d_capabilities()
    explore_video_capabilities()
    explore_game_specific_features()
    test_image_editing()
    check_usage_and_limits()
    
    # Plán využitia
    create_game_development_plan()
    
    print("\n" + "=" * 70)
    print("🎯 ZHRNUTIE:")
    print("✅ Vaše Scenario API je plne funkčné")
    print("🎨 Máte prístup k pokročilým AI nástrojom")
    print("🎮 Perfektné pre generovanie herných assetov")
    print("💡 Odporúčam začať s jednoduchými text-to-image testami")
    
    print("\n🚀 ĎALŠIE KROKY:")
    print("1. Vytvorte si vlastný trénovací dataset")
    print("2. Natrénujte model na váš herný štýl")
    print("3. Integrujte API do vášho herného workflow")
    print("4. Experimentujte s rôznymi modalitami")

if __name__ == "__main__":
    main()
