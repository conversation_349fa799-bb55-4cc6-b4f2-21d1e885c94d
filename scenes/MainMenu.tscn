[gd_scene load_steps=4 format=3 uid="uid://bh8x2ywqxqxr"]

[ext_resource type="Script" uid="uid://cjcy3yiirqptw" path="res://scripts/MainMenu.gd" id="1_main"]
[ext_resource type="PackedScene" uid="uid://bqxvn8ywqxqxr" path="res://scenes/ui/MobileMenuButton.tscn" id="2_button"]
[ext_resource type="PackedScene" path="res://scenes/ui/ChaptersCarousel.tscn" id="3_carousel"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main")
metadata/_edit_use_anchors_ = true

[node name="SafeAreaContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="BackgroundLayer" type="Control" parent="SafeAreaContainer"]
layout_mode = 2

[node name="DynamicBackground" type="TextureRect" parent="SafeAreaContainer/BackgroundLayer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
stretch_mode = 6

[node name="OverlayGradient" type="ColorRect" parent="SafeAreaContainer/BackgroundLayer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.1, 0.1, 0.15, 0.3)

[node name="AtmosphericEffects" type="Node2D" parent="SafeAreaContainer/BackgroundLayer"]

[node name="FogParticles" type="GPUParticles2D" parent="SafeAreaContainer/BackgroundLayer/AtmosphericEffects"]
position = Vector2(540, 100)
amount = 30
lifetime = 12.0
visibility_rect = Rect2(-100, -50, 1280, 200)

[node name="RavenAnimation" type="AnimatedSprite2D" parent="SafeAreaContainer/BackgroundLayer/AtmosphericEffects"]
position = Vector2(200, 300)
scale = Vector2(2, 2)

[node name="LightningFlash" type="ColorRect" parent="SafeAreaContainer/BackgroundLayer/AtmosphericEffects"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(1, 1, 1, 0)

[node name="LightningAnimator" type="AnimationPlayer" parent="SafeAreaContainer/BackgroundLayer/AtmosphericEffects"]

[node name="UILayer" type="Control" parent="SafeAreaContainer"]
layout_mode = 2

[node name="TopSection" type="VBoxContainer" parent="SafeAreaContainer/UILayer"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_top = 100.0
offset_bottom = 500.0
grow_horizontal = 2
alignment = 1

[node name="GameTitle" type="RichTextLabel" parent="SafeAreaContainer/UILayer/TopSection"]
custom_minimum_size = Vector2(0, 200)
layout_mode = 2
bbcode_enabled = true
text = "[center][font_size=56][color=#D4AF37]PREKLIATE[/color]
[color=#8B0000]DEDIČSTVO[/color][/font_size]
[font_size=24][color=#8B4513]Cursed Legacy[/color][/font_size][/center]"
fit_content = true
scroll_active = false

[node name="TitleOrnament" type="TextureRect" parent="SafeAreaContainer/UILayer/TopSection"]
custom_minimum_size = Vector2(600, 120)
layout_mode = 2
stretch_mode = 4

[node name="LoadingIndicator" type="HBoxContainer" parent="SafeAreaContainer/UILayer/TopSection"]
layout_mode = 2
alignment = 1

[node name="LoadingSpinner" type="TextureRect" parent="SafeAreaContainer/UILayer/TopSection/LoadingIndicator"]
custom_minimum_size = Vector2(32, 32)
layout_mode = 2
stretch_mode = 4

[node name="LoadingLabel" type="Label" parent="SafeAreaContainer/UILayer/TopSection/LoadingIndicator"]
layout_mode = 2
text = "Generovanie assetov..."
horizontal_alignment = 1

[node name="ChaptersSection" type="VBoxContainer" parent="SafeAreaContainer/UILayer"]
visible = false
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_top = 500.0
offset_bottom = 1100.0
grow_horizontal = 2

[node name="ChaptersCarousel" parent="SafeAreaContainer/UILayer/ChaptersSection" instance=ExtResource("3_carousel")]
layout_mode = 2

[node name="MiddleSection" type="VBoxContainer" parent="SafeAreaContainer/UILayer"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_top = 600.0
offset_bottom = 1400.0
grow_horizontal = 2
alignment = 1

[node name="MenuFrame" type="NinePatchRect" parent="SafeAreaContainer/UILayer/MiddleSection"]
custom_minimum_size = Vector2(800, 600)
layout_mode = 2
patch_margin_left = 60
patch_margin_top = 60
patch_margin_right = 60
patch_margin_bottom = 60

[node name="ButtonContainer" type="VBoxContainer" parent="SafeAreaContainer/UILayer/MiddleSection"]
custom_minimum_size = Vector2(600, 500)
layout_mode = 2
alignment = 1

[node name="MenuDivider1" type="TextureRect" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer"]
custom_minimum_size = Vector2(500, 16)
layout_mode = 2
stretch_mode = 4

[node name="NovaHraButton" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer" instance=ExtResource("2_button")]
custom_minimum_size = Vector2(500, 80)
layout_mode = 2
button_text = "NOVÁ HRA"
button_action = "new_game"

[node name="MenuDivider2" type="TextureRect" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer"]
custom_minimum_size = Vector2(400, 8)
layout_mode = 2
stretch_mode = 4

[node name="PokracovatButton" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer" instance=ExtResource("2_button")]
custom_minimum_size = Vector2(500, 80)
layout_mode = 2
button_text = "POKRAČOVAŤ"
button_action = "continue_game"

[node name="MenuDivider3" type="TextureRect" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer"]
custom_minimum_size = Vector2(400, 8)
layout_mode = 2
stretch_mode = 4

[node name="NastaveniaButton" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer" instance=ExtResource("2_button")]
custom_minimum_size = Vector2(500, 80)
layout_mode = 2
button_text = "NASTAVENIA"
button_action = "settings"

[node name="MenuDivider4" type="TextureRect" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer"]
custom_minimum_size = Vector2(400, 8)
layout_mode = 2
stretch_mode = 4

[node name="GaleriaButton" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer" instance=ExtResource("2_button")]
custom_minimum_size = Vector2(500, 80)
layout_mode = 2
button_text = "GALÉRIA"
button_action = "gallery"

[node name="MenuDivider5" type="TextureRect" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer"]
custom_minimum_size = Vector2(400, 8)
layout_mode = 2
stretch_mode = 4

[node name="UkoncitButton" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer" instance=ExtResource("2_button")]
custom_minimum_size = Vector2(500, 80)
layout_mode = 2
button_text = "UKONČIŤ"
button_action = "quit"

[node name="MenuDivider6" type="TextureRect" parent="SafeAreaContainer/UILayer/MiddleSection/ButtonContainer"]
custom_minimum_size = Vector2(500, 16)
layout_mode = 2
stretch_mode = 4

[node name="BottomSection" type="Control" parent="SafeAreaContainer/UILayer"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -200.0
grow_horizontal = 2
grow_vertical = 0

[node name="StatusContainer" type="VBoxContainer" parent="SafeAreaContainer/UILayer/BottomSection"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = 50.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="ProgressContainer" type="HBoxContainer" parent="SafeAreaContainer/UILayer/BottomSection/StatusContainer"]
layout_mode = 2
alignment = 1

[node name="ProgressSpinner" type="TextureRect" parent="SafeAreaContainer/UILayer/BottomSection/StatusContainer/ProgressContainer"]
custom_minimum_size = Vector2(48, 48)
layout_mode = 2
stretch_mode = 4

[node name="ProgressLabel" type="Label" parent="SafeAreaContainer/UILayer/BottomSection/StatusContainer/ProgressContainer"]
layout_mode = 2
text = "Načítavam..."
horizontal_alignment = 1

[node name="VersionLabel" type="Label" parent="SafeAreaContainer/UILayer/BottomSection"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -120.0
offset_top = -40.0
grow_horizontal = 0
grow_vertical = 0
text = "v1.0.0 - Gothic Edition"
horizontal_alignment = 2

[node name="CreditsLabel" type="Label" parent="SafeAreaContainer/UILayer/BottomSection"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -40.0
offset_right = 300.0
grow_vertical = 0
text = "Powered by Scenario AI"
