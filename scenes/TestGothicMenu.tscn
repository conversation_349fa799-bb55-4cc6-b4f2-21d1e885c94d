[gd_scene load_steps=3 format=3 uid="uid://test_gothic_menu"]

[ext_resource type="Script" path="res://scripts/TestGothicMenu.gd" id="1_test"]
[ext_resource type="PackedScene" uid="uid://bh8x2ywqxqxr" path="res://scenes/MainMenu.tscn" id="2_main_menu"]

[node name="TestGothicMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_test")

[node name="MainMenu" parent="." instance=ExtResource("2_main_menu")]
layout_mode = 1

[node name="TestUI" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="TestPanel" type="Panel" parent="TestUI"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -300.0
offset_bottom = 400.0
grow_horizontal = 0

[node name="TestContainer" type="VBoxContainer" parent="TestUI/TestPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="TestTitle" type="Label" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2
text = "Gothic Menu Test"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2

[node name="GenerateAssetsButton" type="Button" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2
text = "Generate All Assets"

[node name="GenerateMainMenuButton" type="Button" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2
text = "Generate Main Menu Assets"

[node name="GenerateFallbacksButton" type="Button" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2
text = "Generate Fallbacks"

[node name="HSeparator2" type="HSeparator" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2

[node name="TestPerformanceButton" type="Button" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2
text = "Test Performance"

[node name="TestTouchButton" type="Button" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2
text = "Test Touch Targets"

[node name="HSeparator3" type="HSeparator" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2

[node name="StatusLabel" type="Label" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2
text = "Ready for testing..."
autowrap_mode = 2

[node name="ProgressBar" type="ProgressBar" parent="TestUI/TestPanel/TestContainer"]
layout_mode = 2
value = 0.0

[node name="ToggleTestUIButton" type="Button" parent="TestUI"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -100.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = 40.0
grow_horizontal = 0
text = "Hide Test UI"
