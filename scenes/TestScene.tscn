[gd_scene load_steps=2 format=3 uid="uid://8wit4ual3slf"]

[ext_resource type="Script" uid="uid://b1c35kuofqwim" path="res://scripts/TestScene.gd" id="1_test"]

[node name="TestScene" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_test")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.1, 0.15, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -400.0
offset_right = 300.0
offset_bottom = 400.0

[node name="Title" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "PREKLIATE DEDIČSTVO - TEST SUITE"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="TestButtons" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="TestAssetGeneration" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
text = "Test Asset Generation"

[node name="TestPerformance" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
text = "Test Performance Modes"

[node name="TestBattery" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
text = "Test Battery Optimization"

[node name="TestUI" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
text = "Test UI Scaling"

[node name="TestInput" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
text = "Test Mobile Input"

[node name="ClearCache" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
text = "Clear Asset Cache"

[node name="BackToMenu" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
text = "Back to Main Menu"

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="StatusLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Ready for testing..."
horizontal_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="VBoxContainer"]
layout_mode = 2
visible = false

[node name="LogContainer" type="ScrollContainer" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 300)

[node name="LogText" type="RichTextLabel" parent="VBoxContainer/LogContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
bbcode_enabled = true
text = "[color=green]Test log will appear here...[/color]"
