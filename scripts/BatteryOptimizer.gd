extends Node

signal battery_low()
signal battery_critical()
signal power_mode_changed(is_low_power: bool)

var battery_level: float = 1.0
var is_charging: bool = false
var is_low_power_mode: bool = false
var battery_check_timer: Timer

# Battery thresholds
const BATTERY_LOW_THRESHOLD = 0.2
const BATTERY_CRITICAL_THRESHOLD = 0.1
const BATTERY_CHECK_INTERVAL = 30.0  # Check every 30 seconds

# Power saving features
var original_max_fps: int = 60
var original_particle_amounts: Dictionary = {}
var power_save_active: bool = false

func _ready():
	print("BatteryOptimizer: Initializing...")
	
	# Setup battery monitoring timer
	battery_check_timer = Timer.new()
	battery_check_timer.wait_time = BATTERY_CHECK_INTERVAL
	battery_check_timer.timeout.connect(_check_battery_status)
	battery_check_timer.autostart = true
	add_child(battery_check_timer)
	
	# Store original settings
	original_max_fps = Engine.max_fps
	
	# Initial battery check
	_check_battery_status()

func _check_battery_status():
	"""Check current battery status and apply optimizations if needed"""
	
	if not OS.has_feature("mobile"):
		return  # Skip battery optimization on desktop
	
	# Get battery info (platform-specific implementation would be needed)
	_update_battery_info()
	
	# Apply battery-based optimizations
	if battery_level <= BATTERY_CRITICAL_THRESHOLD:
		_enable_critical_power_save()
		emit_signal("battery_critical")
	elif battery_level <= BATTERY_LOW_THRESHOLD:
		_enable_power_save_mode()
		emit_signal("battery_low")
	elif power_save_active and battery_level > BATTERY_LOW_THRESHOLD + 0.1:
		_disable_power_save_mode()

func _update_battery_info():
	"""Update battery information (placeholder for platform-specific implementation)"""
	
	# This would require platform-specific plugins for real battery info
	# For now, we'll simulate battery monitoring based on time
	
	# Simulate battery drain over time (for testing)
	if not is_charging:
		battery_level = max(0.0, battery_level - 0.001)  # Slow drain for testing
	
	# Check if device is in low power mode (would need platform-specific check)
	var new_low_power_mode = battery_level < BATTERY_LOW_THRESHOLD
	if new_low_power_mode != is_low_power_mode:
		is_low_power_mode = new_low_power_mode
		emit_signal("power_mode_changed", is_low_power_mode)

func _enable_power_save_mode():
	"""Enable power saving optimizations"""
	
	if power_save_active:
		return
	
	print("BatteryOptimizer: Enabling power save mode")
	power_save_active = true
	
	# Reduce frame rate
	Engine.max_fps = 30
	
	# Reduce particle effects
	_reduce_particle_effects()
	
	# Disable non-essential animations
	_disable_non_essential_animations()
	
	# Reduce audio processing
	_optimize_audio_for_battery()
	
	# Notify performance manager
	if has_node("/root/MobilePerformanceManager"):
		get_node("/root/MobilePerformanceManager").optimize_for_battery()

func _enable_critical_power_save():
	"""Enable critical power saving (more aggressive)"""
	
	print("BatteryOptimizer: Enabling critical power save mode")
	
	# Even more aggressive optimizations
	Engine.max_fps = 20
	
	# Disable all particle effects
	_disable_all_particles()
	
	# Disable all animations except essential UI
	_disable_all_animations()
	
	# Minimize audio
	_minimize_audio()

func _disable_power_save_mode():
	"""Disable power saving optimizations"""
	
	if not power_save_active:
		return
	
	print("BatteryOptimizer: Disabling power save mode")
	power_save_active = false
	
	# Restore frame rate
	Engine.max_fps = original_max_fps
	
	# Restore particle effects
	_restore_particle_effects()
	
	# Re-enable animations
	_enable_animations()
	
	# Restore audio
	_restore_audio()
	
	# Notify performance manager
	if has_node("/root/MobilePerformanceManager"):
		get_node("/root/MobilePerformanceManager").restore_performance()

func _reduce_particle_effects():
	"""Reduce particle effect intensity"""
	
	var particles = get_tree().get_nodes_in_group("particles")
	for particle in particles:
		if particle is GPUParticles2D:
			var gpu_particle = particle as GPUParticles2D
			
			# Store original amount if not already stored
			if not original_particle_amounts.has(particle.get_instance_id()):
				original_particle_amounts[particle.get_instance_id()] = gpu_particle.amount
			
			# Reduce particle count by 50%
			gpu_particle.amount = int(original_particle_amounts[particle.get_instance_id()] * 0.5)

func _disable_all_particles():
	"""Disable all particle effects for critical power save"""
	
	var particles = get_tree().get_nodes_in_group("particles")
	for particle in particles:
		if particle is GPUParticles2D:
			var gpu_particle = particle as GPUParticles2D
			gpu_particle.emitting = false

func _restore_particle_effects():
	"""Restore original particle effect settings"""
	
	var particles = get_tree().get_nodes_in_group("particles")
	for particle in particles:
		if particle is GPUParticles2D:
			var gpu_particle = particle as GPUParticles2D
			
			# Restore original amount
			var particle_id = particle.get_instance_id()
			if original_particle_amounts.has(particle_id):
				gpu_particle.amount = original_particle_amounts[particle_id]
			
			# Re-enable emission
			gpu_particle.emitting = true

func _disable_non_essential_animations():
	"""Disable non-essential animations to save power"""
	
	var tweens = get_tree().get_nodes_in_group("non_essential_animations")
	for tween in tweens:
		if tween is Tween:
			tween.pause()

func _disable_all_animations():
	"""Disable all animations for critical power save"""
	
	var animation_players = get_tree().get_nodes_in_group("animations")
	for player in animation_players:
		if player is AnimationPlayer:
			player.pause()

func _enable_animations():
	"""Re-enable animations"""
	
	var animation_players = get_tree().get_nodes_in_group("animations")
	for player in animation_players:
		if player is AnimationPlayer:
			player.play()
	
	var tweens = get_tree().get_nodes_in_group("non_essential_animations")
	for tween in tweens:
		if tween is Tween:
			tween.play()

func _optimize_audio_for_battery():
	"""Optimize audio settings for battery life"""
	
	# Reduce audio quality
	var bus_count = AudioServer.get_bus_count()
	for i in range(bus_count):
		# Disable effects on all buses except master
		if i > 0:
			var effect_count = AudioServer.get_bus_effect_count(i)
			for j in range(effect_count):
				AudioServer.set_bus_effect_enabled(i, j, false)

func _minimize_audio():
	"""Minimize audio for critical power save"""
	
	# Mute all buses except essential UI sounds
	var bus_count = AudioServer.get_bus_count()
	for i in range(1, bus_count):  # Skip master bus
		AudioServer.set_bus_mute(i, true)

func _restore_audio():
	"""Restore audio settings"""
	
	# Unmute all buses
	var bus_count = AudioServer.get_bus_count()
	for i in range(bus_count):
		AudioServer.set_bus_mute(i, false)
	
	# Re-enable effects
	for i in range(bus_count):
		var effect_count = AudioServer.get_bus_effect_count(i)
		for j in range(effect_count):
			AudioServer.set_bus_effect_enabled(i, j, true)

func get_battery_level() -> float:
	"""Get current battery level (0.0 - 1.0)"""
	return battery_level

func is_battery_low() -> bool:
	"""Check if battery is low"""
	return battery_level <= BATTERY_LOW_THRESHOLD

func is_battery_critical() -> bool:
	"""Check if battery is critically low"""
	return battery_level <= BATTERY_CRITICAL_THRESHOLD

func is_power_save_active() -> bool:
	"""Check if power save mode is active"""
	return power_save_active

func force_power_save_mode(enabled: bool):
	"""Force power save mode on/off"""
	
	if enabled:
		_enable_power_save_mode()
	else:
		_disable_power_save_mode()

func get_battery_stats() -> Dictionary:
	"""Get battery and power statistics"""
	return {
		"battery_level": battery_level,
		"is_charging": is_charging,
		"is_low_power_mode": is_low_power_mode,
		"power_save_active": power_save_active,
		"is_battery_low": is_battery_low(),
		"is_battery_critical": is_battery_critical()
	}

# Platform-specific battery monitoring (would need plugins)
func _get_platform_battery_info():
	"""Get platform-specific battery information"""
	
	# This would require platform-specific implementations
	# For Android: JNI calls to BatteryManager
	# For iOS: Native plugin calls
	
	# Placeholder implementation
	pass
