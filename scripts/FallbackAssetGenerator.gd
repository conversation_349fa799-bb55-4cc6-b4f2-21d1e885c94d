extends Node
class_name FallbackAssetGenerator

# Generate fallback assets for offline use
static func generate_all_fallback_assets():
	print("Generating gothic fallback assets...")

	_generate_background()
	_generate_button_assets()
	_generate_ui_elements()
	_generate_decorative_elements()
	_generate_gothic_elements()

	print("Gothic fallback assets generated successfully!")

static func _generate_gothic_elements():
	"""Generate additional gothic UI elements"""
	print("Generating gothic elements...")

	# Menu dividers
	var divider_image = _create_menu_divider(600, 32)
	var divider_texture = ImageTexture.new()
	divider_texture.create_from_image(divider_image)
	_save_fallback_asset("menu_divider", divider_texture)

	# Gothic border
	var border_image = _create_gothic_border(1080, 200)
	var border_texture = ImageTexture.new()
	border_texture.create_from_image(border_image)
	_save_fallback_asset("gothic_border", border_texture)

	# Button hover state
	var hover_image = _create_gothic_button_hover(400, 120)
	var hover_texture = ImageTexture.new()
	hover_texture.create_from_image(hover_image)
	_save_fallback_asset("button_hover", hover_texture)

static func _generate_background():
	var image = Image.create(1080, 1920, false, Image.FORMAT_RGBA8)
	
	# Create gothic gradient background
	for y in range(1920):
		for x in range(1080):
			var gradient_factor = float(y) / 1920.0
			var base_color = Color(0.05, 0.05, 0.1, 1.0)  # Dark blue-black
			var top_color = Color(0.1, 0.08, 0.15, 1.0)   # Slightly lighter purple
			
			var color = base_color.lerp(top_color, 1.0 - gradient_factor)
			
			# Add some noise for texture
			var noise = (sin(x * 0.01) + cos(y * 0.01)) * 0.02
			color = color + Color(noise, noise, noise * 0.5, 0.0)
			
			image.set_pixel(x, y, color)
	
	var texture = ImageTexture.new()
	texture.create_from_image(image)
	
	_save_fallback_asset("main_background", texture)

static func _generate_button_assets():
	# Normal button
	var normal_image = _create_gothic_button(400, 120, false)
	var normal_texture = ImageTexture.new()
	normal_texture.create_from_image(normal_image)
	_save_fallback_asset("button_normal", normal_texture)
	
	# Pressed button (slightly darker)
	var pressed_image = _create_gothic_button(400, 120, true)
	var pressed_texture = ImageTexture.new()
	pressed_texture.create_from_image(pressed_image)
	_save_fallback_asset("button_pressed", pressed_texture)

static func _create_gothic_button(width: int, height: int, pressed: bool) -> Image:
	var image = Image.create(width, height, false, Image.FORMAT_RGBA8)
	
	var base_color = Color(0.3, 0.25, 0.2, 0.9) if not pressed else Color(0.25, 0.2, 0.15, 0.9)
	var border_color = Color(0.6, 0.5, 0.3, 1.0)
	var highlight_color = Color(0.4, 0.35, 0.25, 1.0)
	
	# Fill background
	image.fill(base_color)
	
	# Draw border
	for x in range(width):
		for y in range(height):
			if x < 3 or x >= width - 3 or y < 3 or y >= height - 3:
				image.set_pixel(x, y, border_color)
			elif x < 6 or x >= width - 6 or y < 6 or y >= height - 6:
				image.set_pixel(x, y, highlight_color)
	
	# Add some texture
	for i in range(100):
		var x = randi() % width
		var y = randi() % height
		var noise_color = base_color + Color(randf() * 0.1 - 0.05, randf() * 0.1 - 0.05, randf() * 0.1 - 0.05, 0.0)
		image.set_pixel(x, y, noise_color)
	
	return image

static func _generate_ui_elements():
	# Menu frame
	var frame_image = _create_gothic_frame(800, 1200)
	var frame_texture = ImageTexture.new()
	frame_texture.create_from_image(frame_image)
	_save_fallback_asset("menu_frame", frame_texture)
	
	# Loading spinner
	var spinner_image = _create_loading_spinner(128)
	var spinner_texture = ImageTexture.new()
	spinner_texture.create_from_image(spinner_image)
	_save_fallback_asset("loading_spinner", spinner_texture)

static func _create_gothic_frame(width: int, height: int) -> Image:
	var image = Image.create(width, height, false, Image.FORMAT_RGBA8)
	
	var frame_color = Color(0.4, 0.3, 0.2, 0.8)
	var border_color = Color(0.6, 0.5, 0.3, 1.0)
	var inner_color = Color(0.0, 0.0, 0.0, 0.0)  # Transparent center
	
	var frame_thickness = 40
	
	# Fill with transparent
	image.fill(inner_color)
	
	# Draw frame
	for x in range(width):
		for y in range(height):
			if x < frame_thickness or x >= width - frame_thickness or y < frame_thickness or y >= height - frame_thickness:
				if x < 5 or x >= width - 5 or y < 5 or y >= height - 5:
					image.set_pixel(x, y, border_color)
				else:
					image.set_pixel(x, y, frame_color)
	
	return image

static func _create_loading_spinner(size: int) -> Image:
	var image = Image.create(size, size, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	var center = Vector2(size / 2, size / 2)
	var radius = size / 2 - 10
	var color = Color(0.8, 0.7, 0.5, 1.0)
	
	# Draw spinning circle segments
	for angle in range(0, 360, 10):
		var rad = deg_to_rad(angle)
		var x = center.x + cos(rad) * radius
		var y = center.y + sin(rad) * radius
		
		var alpha = float(angle) / 360.0
		var segment_color = Color(color.r, color.g, color.b, alpha)
		
		# Draw small circle at this position
		for dx in range(-3, 4):
			for dy in range(-3, 4):
				if dx * dx + dy * dy <= 9:
					var px = int(x + dx)
					var py = int(y + dy)
					if px >= 0 and px < size and py >= 0 and py < size:
						image.set_pixel(px, py, segment_color)
	
	return image

static func _generate_decorative_elements():
	# Title ornament
	var ornament_image = _create_title_ornament(600, 200)
	var ornament_texture = ImageTexture.new()
	ornament_texture.create_from_image(ornament_image)
	_save_fallback_asset("title_ornament", ornament_texture)
	
	# Fog particle
	var fog_image = _create_fog_particle(64)
	var fog_texture = ImageTexture.new()
	fog_texture.create_from_image(fog_image)
	_save_fallback_asset("particle_fog", fog_texture)
	
	# Raven silhouette
	var raven_image = _create_raven_silhouette(128)
	var raven_texture = ImageTexture.new()
	raven_texture.create_from_image(raven_image)
	_save_fallback_asset("raven_silhouette", raven_texture)

static func _create_title_ornament(width: int, height: int) -> Image:
	var image = Image.create(width, height, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	var color = Color(0.8, 0.7, 0.4, 0.8)
	var center_y = height / 2
	
	# Draw decorative lines
	for x in range(50, width - 50):
		var y_offset = sin(float(x) / 50.0) * 10
		var y = center_y + int(y_offset)
		
		if y >= 0 and y < height:
			image.set_pixel(x, y, color)
			if y > 0:
				image.set_pixel(x, y - 1, Color(color.r, color.g, color.b, color.a * 0.5))
			if y < height - 1:
				image.set_pixel(x, y + 1, Color(color.r, color.g, color.b, color.a * 0.5))
	
	# Add corner decorations
	_draw_corner_decoration(image, 20, 20, color)
	_draw_corner_decoration(image, width - 40, 20, color)
	_draw_corner_decoration(image, 20, height - 40, color)
	_draw_corner_decoration(image, width - 40, height - 40, color)
	
	return image

static func _draw_corner_decoration(image: Image, x: int, y: int, color: Color):
	# Simple corner decoration
	for i in range(20):
		for j in range(20):
			if (i + j) < 15:
				var px = x + i
				var py = y + j
				if px < image.get_width() and py < image.get_height():
					image.set_pixel(px, py, color)

static func _create_fog_particle(size: int) -> Image:
	var image = Image.create(size, size, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	var center = Vector2(size / 2, size / 2)
	var max_radius = size / 2
	
	for x in range(size):
		for y in range(size):
			var distance = Vector2(x, y).distance_to(center)
			if distance <= max_radius:
				var alpha = 1.0 - (distance / max_radius)
				alpha = alpha * alpha  # Smooth falloff
				var color = Color(0.9, 0.9, 1.0, alpha * 0.3)
				image.set_pixel(x, y, color)
	
	return image

static func _create_raven_silhouette(size: int) -> Image:
	var image = Image.create(size, size, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	var color = Color(0.1, 0.1, 0.1, 1.0)
	
	# Simple raven shape (very basic)
	var center_x = size / 2
	var center_y = size / 2
	
	# Body
	for y in range(center_y - 10, center_y + 20):
		for x in range(center_x - 8, center_x + 8):
			if x >= 0 and x < size and y >= 0 and y < size:
				image.set_pixel(x, y, color)
	
	# Head
	for y in range(center_y - 20, center_y - 5):
		for x in range(center_x - 6, center_x + 6):
			if x >= 0 and x < size and y >= 0 and y < size:
				var distance = Vector2(x - center_x, y - (center_y - 12)).length()
				if distance <= 8:
					image.set_pixel(x, y, color)
	
	# Wings
	for y in range(center_y - 5, center_y + 10):
		for x in range(center_x - 25, center_x - 8):
			if x >= 0 and x < size and y >= 0 and y < size:
				image.set_pixel(x, y, color)
		for x in range(center_x + 8, center_x + 25):
			if x >= 0 and x < size and y >= 0 and y < size:
				image.set_pixel(x, y, color)
	
	return image

static func _save_fallback_asset(asset_name: String, texture: ImageTexture):
	var dir = DirAccess.open("res://")
	if not dir.dir_exists("fallback_assets"):
		dir.make_dir("fallback_assets")
	
	var image = texture.get_image()
	var path = "res://fallback_assets/" + asset_name + ".png"
	
	var error = image.save_png(path)
	if error == OK:
		print("Saved gothic fallback asset: ", path)
	else:
		print("Failed to save fallback asset: ", asset_name, " - Error: ", error)

static func _create_menu_divider(width: int, height: int) -> Image:
	"""Create gothic menu divider"""
	var image = Image.create(width, height, false, Image.FORMAT_RGBA8)

	var divider_color = Color(0.4, 0.35, 0.3, 0.6)
	var ornament_color = Color(0.6, 0.5, 0.4, 0.8)

	for y in range(height):
		for x in range(width):
			var color = Color.TRANSPARENT

			# Main decorative line
			if y >= height/2 - 2 and y <= height/2 + 2:
				color = divider_color

				# Add ornamental details every 50 pixels
				if x % 50 < 10 and sin(x * 0.2) > 0:
					color = ornament_color

			image.set_pixel(x, y, color)

	return image

static func _create_gothic_border(width: int, height: int) -> Image:
	"""Create gothic architectural border"""
	var image = Image.create(width, height, false, Image.FORMAT_RGBA8)

	var border_color = Color(0.3, 0.25, 0.2, 0.7)
	var detail_color = Color(0.5, 0.4, 0.3, 0.9)

	for y in range(height):
		for x in range(width):
			var color = Color.TRANSPARENT

			# Create gothic architectural pattern
			if y < 30 or y > height - 30:
				# Top and bottom borders with gothic details
				if sin(x * 0.05) * cos(y * 0.1) > 0.3:
					color = border_color

					# Add carved details
					if sin(x * 0.1) > 0.7:
						color = detail_color

			image.set_pixel(x, y, color)

	return image

static func _create_gothic_button_hover(width: int, height: int) -> Image:
	"""Create gothic button with hover effect"""
	var image = Image.create(width, height, false, Image.FORMAT_RGBA8)

	var stone_color = Color(0.4, 0.35, 0.3, 1.0)   # Slightly brighter than normal
	var highlight_color = Color(0.5, 0.45, 0.4, 1.0)
	var shadow_color = Color(0.3, 0.25, 0.2, 1.0)

	var center_x = width / 2
	var center_y = height / 2

	for y in range(height):
		for x in range(width):
			var color = stone_color

			# 3D carved stone effect
			var edge_distance = min(min(x, width - x), min(y, height - y))
			if edge_distance < 8:
				if x < width/2 and y < height/2:
					color = highlight_color  # Top-left highlight
				else:
					color = shadow_color     # Bottom-right shadow

			# Add mystical blue glow for hover effect
			var distance_from_center = sqrt((x - center_x) * (x - center_x) + (y - center_y) * (y - center_y))
			var max_distance = sqrt(center_x * center_x + center_y * center_y)
			var glow_factor = max(0, 1.0 - distance_from_center / max_distance) * 0.3

			# Blue mystical glow
			color = color + Color(glow_factor * 0.2, glow_factor * 0.4, glow_factor * 0.8, 0)

			# Add stone texture
			var noise = sin(x * 0.1) * cos(y * 0.1) * 0.05
			color = color + Color(noise, noise, noise, 0)

			image.set_pixel(x, y, color)

	return image
