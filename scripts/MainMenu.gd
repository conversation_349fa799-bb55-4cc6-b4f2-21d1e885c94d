extends Control

# UI References - Gothic Main Menu
@onready var safe_area_container: MarginContainer = $SafeAreaContainer
@onready var dynamic_background: TextureRect = $SafeAreaContainer/BackgroundLayer/DynamicBackground
@onready var overlay_gradient: ColorRect = $SafeAreaContainer/BackgroundLayer/OverlayGradient
@onready var fog_particles: GPUParticles2D = $SafeAreaContainer/BackgroundLayer/AtmosphericEffects/FogParticles
@onready var raven_animation: AnimatedSprite2D = $SafeAreaContainer/BackgroundLayer/AtmosphericEffects/RavenAnimation
@onready var lightning_flash: ColorRect = $SafeAreaContainer/BackgroundLayer/AtmosphericEffects/LightningFlash
@onready var lightning_animator: AnimationPlayer = $SafeAreaContainer/BackgroundLayer/AtmosphericEffects/LightningAnimator

# Top section - Title and loading
@onready var game_title: RichTextLabel = $SafeAreaContainer/UILayer/TopSection/GameTitle
@onready var title_ornament: TextureRect = $SafeAreaContainer/UILayer/TopSection/TitleOrnament
@onready var loading_indicator: HBoxContainer = $SafeAreaContainer/UILayer/TopSection/LoadingIndicator
@onready var loading_spinner: TextureRect = $SafeAreaContainer/UILayer/TopSection/LoadingIndicator/LoadingSpinner
@onready var loading_label: Label = $SafeAreaContainer/UILayer/TopSection/LoadingIndicator/LoadingLabel

# Chapters section (hidden initially)
@onready var chapters_section: VBoxContainer = $SafeAreaContainer/UILayer/ChaptersSection
@onready var chapters_carousel: ChaptersCarousel = $SafeAreaContainer/UILayer/ChaptersSection/ChaptersCarousel

# Main menu section
@onready var menu_frame: NinePatchRect = $SafeAreaContainer/UILayer/MiddleSection/MenuFrame
@onready var button_container: VBoxContainer = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer

# Menu buttons
@onready var nova_hra_button: MobileMenuButton = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer/NovaHraButton
@onready var pokracovat_button: MobileMenuButton = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer/PokracovatButton
@onready var nastavenia_button: MobileMenuButton = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer/NastaveniaButton
@onready var galeria_button: MobileMenuButton = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer/GaleriaButton
@onready var ukoncit_button: MobileMenuButton = $SafeAreaContainer/UILayer/MiddleSection/ButtonContainer/UkoncitButton

# Bottom section - Status and version
@onready var progress_container: HBoxContainer = $SafeAreaContainer/UILayer/BottomSection/StatusContainer/ProgressContainer
@onready var progress_spinner: TextureRect = $SafeAreaContainer/UILayer/BottomSection/StatusContainer/ProgressContainer/ProgressSpinner
@onready var progress_label: Label = $SafeAreaContainer/UILayer/BottomSection/StatusContainer/ProgressContainer/ProgressLabel
@onready var version_label: Label = $SafeAreaContainer/UILayer/BottomSection/VersionLabel
@onready var credits_label: Label = $SafeAreaContainer/UILayer/BottomSection/CreditsLabel

# Loading state
var assets_loaded: bool = false
var main_menu_assets_loaded: bool = false
var loading_rotation_speed: float = 180.0  # degrees per second
var current_loading_asset: String = ""
var total_assets_to_load: int = 0
var loaded_assets_count: int = 0

func _ready():
	print("MainMenu: Initializing Gothic Main Menu...")

	# Setup safe area for mobile devices
	_setup_safe_area()

	# Setup initial UI state
	_setup_initial_ui()

	# Connect to asset manager for AI generation
	_connect_asset_manager()

	# Setup button actions
	_setup_button_actions()

	# Setup atmospheric effects
	_setup_atmospheric_effects()

	# Start asset generation
	_start_asset_generation()

	# Start asset generation
	_start_asset_loading()

func _setup_safe_area():
	if UIScaler:
		UIScaler.apply_safe_area_to_control(safe_area_container)

func _setup_initial_ui():
	print("Setting up initial UI...")

	# Show loading state initially
	_show_loading_state()

	# Hide chapters section initially
	if chapters_section:
		chapters_section.visible = false

	# Setup title with enhanced gothic styling
	if game_title:
		game_title.text = "[center][font_size=56][color=#D4AF37]PREKLIATE[/color]\n[color=#8B0000]DEDIČSTVO[/color][/font_size]\n[font_size=24][color=#8B4513]Cursed Legacy[/color][/font_size][/center]"
		game_title.add_theme_color_override("default_color", Color(0.9, 0.85, 0.7, 1.0))

	# Setup version and credits labels
	if version_label:
		version_label.text = "v1.0.0 - Gothic Edition"
		version_label.add_theme_color_override("font_color", Color(0.7, 0.6, 0.5, 0.8))

	if credits_label:
		credits_label.text = "Powered by Scenario AI"
		credits_label.add_theme_color_override("font_color", Color(0.6, 0.5, 0.4, 0.7))

	# Setup overlay gradient for atmospheric effect
	overlay_gradient.color = Color(0.1, 0.1, 0.15, 0.4)

func _connect_asset_manager():
	if AssetManager:
		AssetManager.asset_generated.connect(_on_asset_generated)
		AssetManager.all_assets_loaded.connect(_on_all_assets_loaded)
		AssetManager.loading_progress.connect(_on_loading_progress)
		print("Connected to AssetManager for gothic asset generation")
	else:
		print("Warning: AssetManager not found!")

func _show_loading_state():
	"""Show loading UI elements"""
	if loading_indicator:
		loading_indicator.visible = true
	if progress_container:
		progress_container.visible = true
	if loading_label:
		loading_label.text = "Generovanie gotických assetov..."

func _hide_loading_state():
	"""Hide loading UI elements"""
	if loading_indicator:
		loading_indicator.visible = false
	if progress_container:
		progress_container.visible = false

func _start_asset_generation():
	"""Start generating main menu assets"""
	print("Starting gothic asset generation...")
	if AssetManager:
		# Generate main menu assets first
		AssetManager.generate_main_menu_assets()
	else:
		print("AssetManager not available, using fallback assets")
		_on_all_assets_loaded()

func _setup_atmospheric_effects():
	"""Setup gothic atmospheric effects"""
	print("Setting up atmospheric effects...")

	# Setup fog particles for gothic atmosphere
	if fog_particles:
		fog_particles.emitting = true
		fog_particles.amount = 30
		fog_particles.lifetime = 12.0

	# Setup lightning flash animation
	if lightning_animator:
		_schedule_random_lightning()

	# Setup raven animation if available
	if raven_animation:
		# Add subtle bobbing animation
		var raven_tween = create_tween()
		raven_tween.set_loops()
		raven_tween.tween_property(raven_animation, "position:y", raven_animation.position.y - 10, 2.0)
		raven_tween.tween_property(raven_animation, "position:y", raven_animation.position.y + 10, 2.0)

func _setup_button_actions():
	print("Setting up button actions...")

	# Setup button texts and actions with gothic styling
	if nova_hra_button:
		nova_hra_button.button_text = "NOVÁ HRA"
		nova_hra_button.button_action = "new_game"
		nova_hra_button.pressed.connect(func(): _on_button_action("new_game"))

	if pokracovat_button:
		pokracovat_button.button_text = "POKRAČOVAŤ"
		pokracovat_button.button_action = "continue_game"
		pokracovat_button.pressed.connect(func(): _on_button_action("continue_game"))
		pokracovat_button.set_enabled(false)  # Disabled until save exists

	if nastavenia_button:
		nastavenia_button.button_text = "NASTAVENIA"
		nastavenia_button.button_action = "settings"
		nastavenia_button.pressed.connect(func(): _on_button_action("settings"))

	if galeria_button:
		galeria_button.button_text = "GALÉRIA"
		galeria_button.button_action = "gallery"
		galeria_button.pressed.connect(func(): _on_button_action("gallery"))

	if ukoncit_button:
		ukoncit_button.button_text = "UKONČIŤ"
		ukoncit_button.button_action = "quit"
	ukoncit_button.pressed.connect(func(): _on_button_action("quit"))

func _setup_chapters_carousel():
	"""Setup chapters carousel functionality"""
	if chapters_carousel:
		chapters_carousel.chapter_selected.connect(_on_chapter_selected)
		print("Chapters carousel connected successfully")

func _start_asset_loading():
	print("MainMenu: Starting asset loading...")
	loading_spinner.visible = true
	
	if AssetManager:
		AssetManager.generate_all_mobile_assets()
	else:
		print("AssetManager not available!")
		_on_all_assets_loaded()  # Fallback



func _setup_fog_particles():
	# Configure fog particles for mobile performance
	fog_particles.emitting = true
	fog_particles.amount = 50  # Reduced for mobile
	fog_particles.lifetime = 8.0
	
	# Setup particle material
	var material = ParticleProcessMaterial.new()
	material.direction = Vector3(1, -0.5, 0)
	material.initial_velocity_min = 20.0
	material.initial_velocity_max = 40.0
	material.gravity = Vector3(0, -10, 0)
	material.scale_min = 0.5
	material.scale_max = 1.5
	material.color = Color(0.8, 0.8, 0.9, 0.3)
	
	fog_particles.process_material = material

func _setup_lightning_animation():
	# Create lightning flash animation
	var animation = Animation.new()
	animation.length = 0.3
	
	# Flash the overlay
	var track = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(track, "SafeAreaContainer/BackgroundLayer/OverlayGradient:color")
	animation.track_insert_key(track, 0.0, Color(0.1, 0.1, 0.15, 0.3))
	animation.track_insert_key(track, 0.1, Color(0.9, 0.9, 1.0, 0.8))
	animation.track_insert_key(track, 0.3, Color(0.1, 0.1, 0.15, 0.3))
	
	var library = AnimationLibrary.new()
	library.add_animation("lightning_flash", animation)
	lightning_flash.add_animation_library("default", library)
	
	# Random lightning flashes
	_schedule_random_lightning()

func _schedule_random_lightning():
	"""Schedule random lightning flashes for gothic atmosphere"""
	var delay = randf_range(15.0, 45.0)  # Random delay between 15-45 seconds
	await get_tree().create_timer(delay).timeout

	if lightning_animator and is_inside_tree():
		lightning_animator.play("lightning_flash")
		print("Lightning flash!")
		_schedule_random_lightning()  # Schedule next flash

func _process(delta):
	# Rotate loading spinners
	if loading_spinner and loading_spinner.visible:
		loading_spinner.rotation_degrees += loading_rotation_speed * delta

	if progress_spinner and progress_spinner.visible:
		progress_spinner.rotation_degrees += loading_rotation_speed * delta

func _on_asset_generated(asset_type: String, texture: ImageTexture):
	print("MainMenu: Asset generated: ", asset_type)
	
	match asset_type:
		"main_background":
			if dynamic_background:
				dynamic_background.texture = texture
		"menu_frame":
			if menu_frame:
				menu_frame.texture = texture
		"title_ornament":
			if title_ornament:
				title_ornament.texture = texture
		"loading_spinner":
			if loading_spinner:
				loading_spinner.texture = texture
		"raven_silhouette":
			if raven_animation:
				# Create simple animation from static texture
				_create_raven_animation(texture)

func _create_raven_animation(texture: ImageTexture):
	# Create a simple bobbing animation for the raven
	var tween = create_tween()
	tween.set_loops()
	tween.tween_property(raven_animation, "position:y", raven_animation.position.y - 10, 2.0)
	tween.tween_property(raven_animation, "position:y", raven_animation.position.y + 10, 2.0)
	
	# Set the texture
	var sprite_frames = SpriteFrames.new()
	sprite_frames.add_animation("default")
	sprite_frames.add_frame("default", texture)
	raven_animation.sprite_frames = sprite_frames
	raven_animation.play("default")

func _on_loading_progress(current: int, total: int, asset_type: String):
	print("MainMenu: Loading progress: ", current, "/", total, " (", asset_type, ")")
	# Could update a progress bar here if desired

func _on_all_assets_loaded():
	print("MainMenu: All gothic assets loaded!")
	assets_loaded = true
	main_menu_assets_loaded = true

	# Hide loading state
	_hide_loading_state()

	# Enable continue button if save exists
	_check_save_file()

	# Start intro animation
	_play_intro_animation()

	# Show chapters section after main menu is ready
	if chapters_section:
		chapters_section.visible = true

func _check_save_file():
	if FileAccess.file_exists("user://savegame.dat"):
		pokracovat_button.set_enabled(true)

func _play_intro_animation():
	# Fade in animation for UI elements
	var ui_elements = [game_title, subtitle, title_ornament, button_container]
	
	for i in range(ui_elements.size()):
		var element = ui_elements[i]
		element.modulate.a = 0.0
		
		var tween = create_tween()
		tween.tween_delay(i * 0.2)
		tween.tween_property(element, "modulate:a", 1.0, 0.5)

func _on_button_action(action: String):
	print("MainMenu: Button action: ", action)
	
	match action:
		"new_game":
			_start_new_game()
		"continue_game":
			_continue_game()
		"settings":
			_open_settings()
		"gallery":
			_open_gallery()
		"quit":
			_quit_game()

func _start_new_game():
	print("Starting new game...")
	# TODO: Transition to game scene
	# get_tree().change_scene_to_file("res://scenes/GameIntro.tscn")

func _continue_game():
	print("Continuing game...")
	# TODO: Load save and transition to game
	# GameManager.load_game()
	# get_tree().change_scene_to_file("res://scenes/GameWorld.tscn")

func _open_settings():
	print("Opening settings...")
	# TODO: Open settings menu
	# get_tree().change_scene_to_file("res://scenes/SettingsMenu.tscn")

func _open_gallery():
	print("Opening gallery...")
	# TODO: Open gallery/achievements
	# get_tree().change_scene_to_file("res://scenes/Gallery.tscn")

func _quit_game():
	print("Quitting game...")
	get_tree().quit()

func _on_chapter_selected(chapter_index: int):
	"""Handle chapter selection from carousel"""
	print("MainMenu: Chapter ", chapter_index + 1, " selected")

	# TODO: Start selected chapter
	# ChapterManager.start_chapter(chapter_index)
	# get_tree().change_scene_to_file("res://scenes/chapters/Chapter" + str(chapter_index + 1) + ".tscn")

	# For now, just show selection feedback
	var chapter_name = ""
	match chapter_index:
		0: chapter_name = "Cesta na Zámok"
		1: chapter_name = "Brána Zámku"
		2: chapter_name = "Pátranie v Zámku"
		3: chapter_name = "Tajné Krídlo"
		4: chapter_name = "Krypty"
		5: chapter_name = "Konfrontácia"
		6: chapter_name = "Epilóg"

	print("Starting chapter: ", chapter_name)
