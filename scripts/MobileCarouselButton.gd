extends MobileMenuButton
class_name MobileCarouselButton

# Specialized button for carousel navigation
# Inherits all mobile optimizations from MobileMenuButton

func _ready():
	super._ready()
	
	# Carousel-specific styling
	_apply_carousel_styling()

func _apply_carousel_styling():
	"""Apply carousel-specific visual styling"""
	
	# Smaller size for navigation arrows
	custom_minimum_size = Vector2(60, 60)
	
	# Circular button style
	if background_nine_patch:
		background_nine_patch.patch_margin_left = 30
		background_nine_patch.patch_margin_right = 30
		background_nine_patch.patch_margin_top = 30
		background_nine_patch.patch_margin_bottom = 30
	
	# Larger font for arrow symbols
	if button_label:
		button_label.add_theme_font_size_override("font_size", 24)
		button_label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6, 1.0))

func set_arrow_direction(direction: String):
	"""Set arrow direction (left/right)"""
	match direction:
		"left":
			button_text = "◀"
			button_action = "carousel_left"
		"right":
			button_text = "▶"
			button_action = "carousel_right"
		_:
			button_text = "●"
			button_action = "carousel_dot"

func set_enabled_state(enabled: bool):
	"""Override enabled state with smooth transition"""
	disabled = not enabled
	
	var target_modulate = Color.WHITE if enabled else Color(0.5, 0.5, 0.5, 0.7)
	var tween = create_tween()
	tween.tween_property(self, "modulate", target_modulate, 0.3)
