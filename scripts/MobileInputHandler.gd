extends Node

signal touch_started(position: Vector2)
signal touch_ended(position: Vector2)
signal touch_moved(position: Vector2, relative: Vector2)
signal swipe_detected(direction: Vector2, strength: float)
signal long_press_detected(position: Vector2)

var touch_start_position: Vector2
var touch_start_time: float
var is_touching: bool = false
var long_press_threshold: float = 0.8  # seconds
var swipe_threshold: float = 100.0     # pixels
var long_press_timer: Timer

func _ready():
	# Create long press timer
	long_press_timer = Timer.new()
	long_press_timer.wait_time = long_press_threshold
	long_press_timer.one_shot = true
	long_press_timer.timeout.connect(_on_long_press_timeout)
	add_child(long_press_timer)

func _input(event):
	if event is InputEventScreenTouch:
		_handle_touch_event(event)
	elif event is InputEventScreenDrag:
		_handle_drag_event(event)
	elif event is InputEventMouseButton:
		# Handle mouse as touch for desktop testing
		_handle_mouse_button_event(event)
	elif event is InputEventMouseMotion:
		# Handle mouse motion as drag for desktop testing
		if Input.is_mouse_button_pressed(MOUSE_BUTTON_LEFT):
			_handle_mouse_motion_event(event)

func _handle_touch_event(event: InputEventScreenTouch):
	if event.pressed:
		_start_touch(event.position)
	else:
		_end_touch(event.position)

func _handle_drag_event(event: InputEventScreenDrag):
	if is_touching:
		emit_signal("touch_moved", event.position, event.relative)

func _handle_mouse_button_event(event: InputEventMouseButton):
	if event.button_index == MOUSE_BUTTON_LEFT:
		if event.pressed:
			_start_touch(event.position)
		else:
			_end_touch(event.position)

func _handle_mouse_motion_event(event: InputEventMouseMotion):
	if is_touching:
		emit_signal("touch_moved", event.position, event.relative)

func _start_touch(position: Vector2):
	is_touching = true
	touch_start_position = position
	touch_start_time = Time.get_unix_time_from_system()
	
	# Start long press timer
	long_press_timer.start()
	
	emit_signal("touch_started", position)

func _end_touch(position: Vector2):
	if not is_touching:
		return
	
	is_touching = false
	long_press_timer.stop()
	
	# Check for swipe
	var swipe_vector = position - touch_start_position
	var swipe_distance = swipe_vector.length()
	
	if swipe_distance > swipe_threshold:
		var swipe_direction = swipe_vector.normalized()
		var swipe_strength = min(swipe_distance / 300.0, 1.0)  # Normalize to 0-1
		emit_signal("swipe_detected", swipe_direction, swipe_strength)
	
	emit_signal("touch_ended", position)

func _on_long_press_timeout():
	if is_touching:
		emit_signal("long_press_detected", touch_start_position)

func enable_haptic_feedback():
	"""Enable haptic feedback for supported devices"""
	# This will be called by UI elements when they need haptic feedback
	if OS.has_feature("mobile"):
		Input.vibrate_handheld(50)  # 50ms vibration

func get_safe_area() -> Rect2:
	"""Get safe area for devices with notches/bezels"""
	var viewport_size = get_viewport().get_visible_rect().size
	var safe_area = DisplayServer.get_display_safe_area()
	
	# Convert to relative coordinates
	var safe_rect = Rect2()
	safe_rect.position = Vector2(safe_area.position) / viewport_size
	safe_rect.size = Vector2(safe_area.size) / viewport_size
	
	return safe_rect
