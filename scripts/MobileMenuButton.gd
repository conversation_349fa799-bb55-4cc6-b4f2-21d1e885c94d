extends Button
class_name MobileMenuButton

@export var button_text: String = "" : set = set_button_text
@export var button_action: String = ""
@export var gothic_style: bool = true
@export var enable_haptic: bool = true
@export var hover_scale: float = 1.05
@export var press_scale: float = 0.95

var normal_texture: ImageTexture
var pressed_texture: ImageTexture
var hover_texture: ImageTexture
var hover_tween: Tween
var press_tween: Tween
var background_nine_patch: NinePatchRect
var button_label: Label

# Gothic font and styling
var gothic_font: Font
var base_font_size: int = 24

func _ready():
	# Setup basic button properties
	flat = true
	custom_minimum_size = UIScaler.get_minimum_touch_size()
	
	# Create background
	_create_background()
	
	# Create label
	_create_label()
	
	# Connect signals
	pressed.connect(_on_button_pressed)
	button_down.connect(_on_button_down)
	button_up.connect(_on_button_up)
	mouse_entered.connect(_on_mouse_entered)
	mouse_exited.connect(_on_mouse_exited)
	
	# Connect to asset manager
	if AssetManager:
		AssetManager.asset_generated.connect(_on_asset_received)
		
		# Request button assets
		_request_button_assets()
	
	# Apply initial styling
	_apply_gothic_styling()

func _create_background():
	background_nine_patch = NinePatchRect.new()
	background_nine_patch.name = "Background"
	add_child(background_nine_patch)
	move_child(background_nine_patch, 0)
	
	# Set anchors to fill parent
	background_nine_patch.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Configure nine patch margins for scalable buttons
	background_nine_patch.patch_margin_left = 20
	background_nine_patch.patch_margin_right = 20
	background_nine_patch.patch_margin_top = 20
	background_nine_patch.patch_margin_bottom = 20

func _create_label():
	button_label = Label.new()
	button_label.name = "Label"
	button_label.text = button_text
	button_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	button_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	button_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	
	add_child(button_label)
	button_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Add some padding
	button_label.add_theme_constant_override("margin_left", 20)
	button_label.add_theme_constant_override("margin_right", 20)
	button_label.add_theme_constant_override("margin_top", 10)
	button_label.add_theme_constant_override("margin_bottom", 10)

func _apply_gothic_styling():
	if not gothic_style:
		return
	
	# Load gothic font if available
	if ResourceLoader.exists("res://fonts/gothic_mobile.ttf"):
		gothic_font = load("res://fonts/gothic_mobile.ttf")
		button_label.add_theme_font_override("font", gothic_font)
	
	# Set font size based on screen scale
	var font_size = UIScaler.get_responsive_font_size(button_text.length(), base_font_size)
	button_label.add_theme_font_size_override("font_size", font_size)
	
	# Gothic color scheme
	button_label.add_theme_color_override("font_color", Color(0.9, 0.85, 0.7, 1.0))  # Antique white
	button_label.add_theme_color_override("font_shadow_color", Color(0.1, 0.1, 0.1, 0.8))
	button_label.add_theme_constant_override("shadow_offset_x", 2)
	button_label.add_theme_constant_override("shadow_offset_y", 2)

func _request_button_assets():
	if AssetManager:
		# Generate assets if not cached
		if not AssetManager.is_asset_cached("button_normal"):
			AssetManager.generate_asset("button_normal")
		else:
			_on_asset_received("button_normal", AssetManager.get_asset("button_normal"))

		if not AssetManager.is_asset_cached("button_pressed"):
			AssetManager.generate_asset("button_pressed")
		else:
			_on_asset_received("button_pressed", AssetManager.get_asset("button_pressed"))

		if not AssetManager.is_asset_cached("button_hover"):
			AssetManager.generate_asset("button_hover")
		else:
			_on_asset_received("button_hover", AssetManager.get_asset("button_hover"))

func _on_asset_received(asset_type: String, texture: ImageTexture):
	match asset_type:
		"button_normal":
			normal_texture = texture
			_update_button_appearance()
		"button_pressed":
			pressed_texture = texture
		"button_hover":
			hover_texture = texture

func _update_button_appearance():
	if normal_texture and background_nine_patch:
		background_nine_patch.texture = normal_texture

func set_button_text(new_text: String):
	button_text = new_text
	if button_label:
		button_label.text = button_text
		# Update font size for new text length
		if gothic_style:
			var font_size = UIScaler.get_responsive_font_size(button_text.length(), base_font_size)
			button_label.add_theme_font_size_override("font_size", font_size)

func _on_button_pressed():
	print("Button pressed: ", button_text, " (", button_action, ")")
	
	# Haptic feedback
	if enable_haptic and OS.has_feature("mobile"):
		MobileInputHandler.enable_haptic_feedback()
	
	# Visual feedback with pressed texture
	if pressed_texture and background_nine_patch:
		background_nine_patch.texture = pressed_texture
		
		# Return to normal texture after short delay
		await get_tree().create_timer(0.1).timeout
		if normal_texture:
			background_nine_patch.texture = normal_texture

func _on_button_down():
	# Scale down animation
	if press_tween:
		press_tween.kill()
	
	press_tween = create_tween()
	press_tween.set_ease(Tween.EASE_OUT)
	press_tween.set_trans(Tween.TRANS_BACK)
	press_tween.tween_property(self, "scale", Vector2(press_scale, press_scale), 0.1)

func _on_button_up():
	# Scale back to normal
	if press_tween:
		press_tween.kill()
	
	press_tween = create_tween()
	press_tween.set_ease(Tween.EASE_OUT)
	press_tween.set_trans(Tween.TRANS_BACK)
	press_tween.tween_property(self, "scale", Vector2(1.0, 1.0), 0.1)

func _on_mouse_entered():
	# Hover effect (also works for touch on mobile)
	if hover_tween:
		hover_tween.kill()

	hover_tween = create_tween()
	hover_tween.set_ease(Tween.EASE_OUT)
	hover_tween.set_trans(Tween.TRANS_SINE)
	hover_tween.tween_property(self, "scale", Vector2(hover_scale, hover_scale), 0.2)

	# Change to hover texture if available
	if hover_texture and background_nine_patch:
		background_nine_patch.texture = hover_texture

	# Subtle glow effect
	if button_label:
		button_label.add_theme_color_override("font_color", Color(1.0, 0.9, 0.7, 1.0))

func _on_mouse_exited():
	# Return to normal scale
	if hover_tween:
		hover_tween.kill()

	hover_tween = create_tween()
	hover_tween.set_ease(Tween.EASE_OUT)
	hover_tween.set_trans(Tween.TRANS_SINE)
	hover_tween.tween_property(self, "scale", Vector2(1.0, 1.0), 0.2)

	# Return to normal texture
	if normal_texture and background_nine_patch:
		background_nine_patch.texture = normal_texture

	# Return to normal color
	if button_label:
		button_label.add_theme_color_override("font_color", Color(0.9, 0.85, 0.7, 1.0))

func set_enabled(enabled: bool):
	disabled = not enabled
	modulate = Color.WHITE if enabled else Color(0.5, 0.5, 0.5, 0.7)

func get_action() -> String:
	return button_action

# Animation methods for special effects
func pulse_animation():
	var pulse_tween = create_tween()
	pulse_tween.set_loops()
	pulse_tween.tween_property(self, "modulate", Color(1.2, 1.1, 0.9, 1.0), 0.8)
	pulse_tween.tween_property(self, "modulate", Color.WHITE, 0.8)

func stop_pulse_animation():
	if hover_tween:
		hover_tween.kill()
	modulate = Color.WHITE
