extends HTTPRequest
class_name ScenarioAPI

signal image_generated(asset_type: String, texture: ImageTexture)
signal generation_failed(asset_type: String, error: String)
signal generation_progress(asset_type: String, progress: float)

const API_BASE_URL = "https://api.cloud.scenario.com/v1"
const API_KEY = "api_StCDGknMcAZP7RKggd5rnfVE"
const API_SECRET = "rJUJf3coKs1cbcxAxNWQ8ynf"

var current_requests: Dictionary = {}
var job_polling_timers: Dictionary = {}

# Mobile-optimized generation settings
const MOBILE_GENERATION_SETTINGS = {
	"guidance": 3.5,      # Optimalizované pre Flux model
	"steps": 20,          # <PERSON><PERSON> krokov pre rýchlej<PERSON>ie generovanie
	"scheduler": "EulerAncestralDiscreteScheduler",
	"modelId": "flux.1-dev"  # Verejný Flux model
}

# Gothic art style base prompt
const ART_STYLE_BASE = "hand-drawn illustration, gothic cartoon style, <PERSON> aesthetic, dark fairy tale art, sketch-like lineart, atmospheric lighting, mobile game art, detailed textures"

# Chapter art style base prompt
const CHAPTER_ART_BASE = "hand-drawn gothic illustration, Tim <PERSON> style, dark fairy tale art, dramatic atmospheric lighting, vintage storybook illustration, detailed lineart, muted color palette, mobile game art, portrait composition"

# Asset-specific prompts for consistent style
const ASSET_PROMPTS = {
	"main_background": ART_STYLE_BASE + ", ancient gothic castle silhouette, dark stormy sky, full moon behind clouds, spooky trees, mobile vertical composition, portrait orientation, misty atmosphere",
	"menu_frame": ART_STYLE_BASE + ", ornate gothic picture frame, carved stone details, ivy decoration, worn ancient texture, UI frame design, transparent center, medieval border",
	"button_normal": ART_STYLE_BASE + ", gothic stone button, carved runes, moss details, weathered texture, game UI element, rectangular shape",
	"button_pressed": ART_STYLE_BASE + ", glowing gothic stone button, magical golden light, activated runes, mystical energy, game UI element, rectangular shape",
	"title_ornament": ART_STYLE_BASE + ", decorative gothic border, medieval manuscript style, ornate corner decorations, calligraphy flourishes, symmetrical design",
	"loading_spinner": ART_STYLE_BASE + ", ancient mystical symbol, rotating magical circle, runic inscriptions, glowing effect, circular design, simple",
	"particle_fog": ART_STYLE_BASE + ", wispy fog particle, translucent mist, ethereal smoke, atmospheric effect, single particle element, soft edges",
	"raven_silhouette": ART_STYLE_BASE + ", black raven silhouette, gothic bird, perched pose, ominous atmosphere, simple clean design, side view",

	# Chapter prompts
	"chapter_1": CHAPTER_ART_BASE + ", horse-drawn carriage on stormy mountain road, lightning illuminating Carpathian mountains, dark pine forest, rain and wind, ominous storm clouds, narrow winding path, gothic atmosphere, 1890s period setting",
	"chapter_2": CHAPTER_ART_BASE + ", massive gothic castle gate with iron bars, heraldic symbols carved in stone, mysterious blood-red inscriptions on ancient door, ravens perched on battlements, moonlight casting shadows, medieval architecture, foreboding entrance",
	"chapter_3": CHAPTER_ART_BASE + ", abandoned castle interior, grand hall with fireplace, ancient weapons on walls, dusty furniture covered in cobwebs, flickering candlelight, ornate gothic architecture, mysterious shadows, empty corridors",
	"chapter_4": CHAPTER_ART_BASE + ", dark stone corridor with mechanical traps, ancient pressure plates in floor, mysterious levers and gears, torch-lit narrow passage, dangerous hidden mechanisms, gothic dungeon architecture, ominous atmosphere",
	"chapter_5": CHAPTER_ART_BASE + ", underground stone catacombs with arched ceilings, ancient burial chambers, mystical symbols carved in walls, eerie candlelight, medieval crypts, stone sarcophagi, underground Gothic architecture, mysterious depths",
	"chapter_6": CHAPTER_ART_BASE + ", dramatic confrontation scene in gothic throne room, ornate stone chamber with mystical circles, ancient magical symbols glowing, dark victorian gothic interior, supernatural energy effects, climactic atmosphere",
	"chapter_7": CHAPTER_ART_BASE + ", dawn breaking over gothic castle, golden sunlight piercing through storm clouds, peaceful resolution atmosphere, castle silhouette against morning sky, hope after darkness, serene gothic landscape"
}

func _ready():
	request_completed.connect(_on_request_completed)
	timeout = 30.0  # 30 second timeout for mobile

func generate_mobile_asset(asset_type: String, width: int, height: int):
	if not ASSET_PROMPTS.has(asset_type):
		emit_signal("generation_failed", asset_type, "Unknown asset type")
		return
	
	var prompt = ASSET_PROMPTS[asset_type]
	var negative_prompt = "blurry, low quality, modern elements, bright neon colors, 3D render, photograph, realistic, text, letters, words"
	
	var headers = _get_auth_headers()
	
	var body = {
		"prompt": prompt,
		"negativePrompt": negative_prompt,
		"width": width,
		"height": height,
		"numSamples": 1,
		"guidance": MOBILE_GENERATION_SETTINGS.guidance,
		"numInferenceSteps": MOBILE_GENERATION_SETTINGS.steps,
		"scheduler": MOBILE_GENERATION_SETTINGS.scheduler,
		"modelId": MOBILE_GENERATION_SETTINGS.modelId,
		"seed": randi()
	}
	
	print("Generating asset: ", asset_type, " with size: ", width, "x", height)
	
	current_requests[get_instance_id()] = {
		"asset_type": asset_type,
		"request_type": "generate"
	}
	
	var url = API_BASE_URL + "/generate/txt2img"
	request(url, headers, HTTPClient.METHOD_POST, JSON.stringify(body))

func _get_auth_headers() -> PackedStringArray:
	var credentials = Marshalls.utf8_to_base64(API_KEY + ":" + API_SECRET)
	return PackedStringArray([
		"Authorization: Basic " + credentials,
		"Content-Type: application/json",
		"Accept: application/json"
	])

func _on_request_completed(result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
	var request_info = current_requests.get(get_instance_id(), {})
	current_requests.erase(get_instance_id())
	
	if request_info.is_empty():
		print("Unknown request completed")
		return
	
	var asset_type = request_info.get("asset_type", "unknown")
	var request_type = request_info.get("request_type", "unknown")
	
	if response_code != 200:
		var error_msg = "HTTP Error: " + str(response_code)
		if body.size() > 0:
			error_msg += " - " + body.get_string_from_utf8()
		emit_signal("generation_failed", asset_type, error_msg)
		return
	
	var json = JSON.new()
	var parse_result = json.parse(body.get_string_from_utf8())
	
	if parse_result != OK:
		emit_signal("generation_failed", asset_type, "Failed to parse JSON response")
		return
	
	var response_data = json.data
	
	match request_type:
		"generate":
			_handle_generation_response(asset_type, response_data)
		"poll_job":
			_handle_job_polling_response(asset_type, response_data)
		"download":
			_handle_image_download(asset_type, body)

func _handle_generation_response(asset_type: String, response_data: Dictionary):
	if response_data.has("job") and response_data.job.has("jobId"):
		var job_id = response_data.job.jobId
		print("Job created for ", asset_type, ": ", job_id)
		_start_job_polling(asset_type, job_id)
	elif response_data.has("images") and response_data.images.size() > 0:
		# Direct image response (some models return immediately)
		var image_url = response_data.images[0].url
		_download_mobile_image(image_url, asset_type)
	else:
		emit_signal("generation_failed", asset_type, "No job ID or images in response")

func _start_job_polling(asset_type: String, job_id: String):
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = 3.0  # Poll every 3 seconds
	timer.timeout.connect(func(): _poll_job_status(asset_type, job_id))
	timer.start()
	
	job_polling_timers[asset_type] = {
		"timer": timer,
		"job_id": job_id,
		"attempts": 0
	}

func _poll_job_status(asset_type: String, job_id: String):
	var polling_info = job_polling_timers.get(asset_type)
	if not polling_info:
		return
	
	polling_info.attempts += 1
	
	# Max 60 attempts (3 minutes)
	if polling_info.attempts > 60:
		_cleanup_job_polling(asset_type)
		emit_signal("generation_failed", asset_type, "Job polling timeout")
		return
	
	var headers = _get_auth_headers()
	var url = API_BASE_URL + "/jobs/" + job_id
	
	current_requests[get_instance_id()] = {
		"asset_type": asset_type,
		"request_type": "poll_job"
	}
	
	request(url, headers, HTTPClient.METHOD_GET)

func _handle_job_polling_response(asset_type: String, response_data: Dictionary):
	if not response_data.has("job"):
		emit_signal("generation_failed", asset_type, "Invalid job response")
		return
	
	var job = response_data.job
	var status = job.get("status", "")
	var progress = job.get("progress", 0.0)
	
	emit_signal("generation_progress", asset_type, progress)
	
	match status:
		"success":
			_cleanup_job_polling(asset_type)
			if job.has("metadata") and job.metadata.has("assetIds") and job.metadata.assetIds.size() > 0:
				var asset_id = job.metadata.assetIds[0]
				_download_asset_by_id(asset_type, asset_id)
			else:
				emit_signal("generation_failed", asset_type, "No asset IDs in successful job")
		"failure", "canceled":
			_cleanup_job_polling(asset_type)
			var error = job.get("error", "Job failed")
			emit_signal("generation_failed", asset_type, error)
		"queued", "processing":
			# Continue polling
			pass

func _download_asset_by_id(asset_type: String, asset_id: String):
	var headers = _get_auth_headers()
	var url = API_BASE_URL + "/assets/" + asset_id
	
	current_requests[get_instance_id()] = {
		"asset_type": asset_type,
		"request_type": "get_asset"
	}
	
	request(url, headers, HTTPClient.METHOD_GET)

func _download_mobile_image(url: String, asset_type: String):
	var http_request = HTTPRequest.new()
	add_child(http_request)
	
	http_request.request_completed.connect(
		func(result, code, headers, body): _handle_image_download(asset_type, body, http_request)
	)
	
	http_request.request(url)

func _handle_image_download(asset_type: String, body: PackedByteArray, http_request: HTTPRequest = null):
	if http_request:
		http_request.queue_free()
	
	if body.size() == 0:
		emit_signal("generation_failed", asset_type, "Empty image data")
		return
	
	var image = Image.new()
	var error = image.load_png_from_buffer(body)
	if error != OK:
		error = image.load_jpg_from_buffer(body)
	
	if error != OK:
		emit_signal("generation_failed", asset_type, "Failed to load image data")
		return
	
	# Mobile optimization - compress for memory efficiency
	if image.get_width() > 2048 or image.get_height() > 2048:
		image.resize(min(image.get_width(), 2048), min(image.get_height(), 2048), Image.INTERPOLATE_LANCZOS)
	
	var texture = ImageTexture.new()
	texture.create_from_image(image)
	
	print("Successfully generated asset: ", asset_type)
	emit_signal("image_generated", asset_type, texture)

func _cleanup_job_polling(asset_type: String):
	var polling_info = job_polling_timers.get(asset_type)
	if polling_info and polling_info.has("timer"):
		polling_info.timer.queue_free()
	job_polling_timers.erase(asset_type)
