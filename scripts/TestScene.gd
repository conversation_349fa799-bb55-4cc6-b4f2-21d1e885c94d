extends Control

@onready var status_label: Label = $VBoxContainer/StatusLabel
@onready var progress_bar: ProgressBar = $VBoxContainer/ProgressBar
@onready var log_text: RichTextLabel = $VBoxContainer/LogContainer/LogText

@onready var test_asset_btn: Button = $VBoxContainer/TestButtons/TestAssetGeneration
@onready var test_performance_btn: Button = $VBoxContainer/TestButtons/TestPerformance
@onready var test_battery_btn: Button = $VBoxContainer/TestButtons/TestBattery
@onready var test_ui_btn: Button = $VBoxContainer/TestButtons/TestUI
@onready var test_input_btn: Button = $VBoxContainer/TestButtons/TestInput
@onready var clear_cache_btn: Button = $VBoxContainer/TestButtons/ClearCache
@onready var back_btn: Button = $VBoxContainer/TestButtons/BackToMenu

var test_running: bool = false

func _ready():
	print("TestScene: Initializing test suite...")
	
	# Connect button signals
	test_asset_btn.pressed.connect(_test_asset_generation)
	test_performance_btn.pressed.connect(_test_performance_modes)
	test_battery_btn.pressed.connect(_test_battery_optimization)
	test_ui_btn.pressed.connect(_test_ui_scaling)
	test_input_btn.pressed.connect(_test_mobile_input)
	clear_cache_btn.pressed.connect(_clear_asset_cache)
	back_btn.pressed.connect(_back_to_menu)
	
	# Connect to managers if available
	if AssetManager:
		AssetManager.asset_generated.connect(_on_asset_generated)
		AssetManager.loading_progress.connect(_on_loading_progress)
		AssetManager.all_assets_loaded.connect(_on_all_assets_loaded)
	
	_log_message("Test suite initialized", "green")

func _test_asset_generation():
	if test_running:
		return
	
	_log_message("=== TESTING ASSET GENERATION ===", "yellow")
	test_running = true
	_set_buttons_enabled(false)
	
	status_label.text = "Testing asset generation..."
	progress_bar.visible = true
	progress_bar.value = 0
	
	if AssetManager:
		_log_message("Starting asset generation test...", "white")
		AssetManager.generate_all_mobile_assets()
	else:
		_log_message("ERROR: AssetManager not available!", "red")
		_test_completed()

func _test_performance_modes():
	if test_running:
		return
	
	_log_message("=== TESTING PERFORMANCE MODES ===", "yellow")
	test_running = true
	_set_buttons_enabled(false)
	
	status_label.text = "Testing performance modes..."
	
	if not has_node("/root/MobilePerformanceManager"):
		_log_message("ERROR: MobilePerformanceManager not available!", "red")
		_test_completed()
		return
	
	var perf_manager = get_node("/root/MobilePerformanceManager")
	
	# Test all performance modes
	var modes = ["HIGH", "MEDIUM", "LOW", "BATTERY_SAVER"]
	
	for i in range(modes.size()):
		var mode = i
		_log_message("Testing performance mode: " + modes[i], "white")
		
		perf_manager.set_performance_mode(mode)
		await get_tree().create_timer(2.0).timeout
		
		var stats = perf_manager.get_performance_stats()
		_log_message("Mode: " + stats.performance_mode + " | FPS: " + str(stats.fps) + " | Memory: " + str(stats.memory_usage_mb) + "MB", "cyan")
	
	_log_message("Performance mode testing completed!", "green")
	_test_completed()

func _test_battery_optimization():
	if test_running:
		return
	
	_log_message("=== TESTING BATTERY OPTIMIZATION ===", "yellow")
	test_running = true
	_set_buttons_enabled(false)
	
	status_label.text = "Testing battery optimization..."
	
	if not has_node("/root/BatteryOptimizer"):
		_log_message("ERROR: BatteryOptimizer not available!", "red")
		_test_completed()
		return
	
	var battery_optimizer = get_node("/root/BatteryOptimizer")
	
	# Test normal battery level
	_log_message("Testing normal battery level (80%)", "white")
	battery_optimizer.battery_level = 0.8
	battery_optimizer._check_battery_status()
	await get_tree().create_timer(1.0).timeout
	
	var stats = battery_optimizer.get_battery_stats()
	_log_message("Battery: " + str(stats.battery_level * 100) + "% | Power Save: " + str(stats.power_save_active), "cyan")
	
	# Test low battery
	_log_message("Testing low battery level (15%)", "white")
	battery_optimizer.battery_level = 0.15
	battery_optimizer._check_battery_status()
	await get_tree().create_timer(1.0).timeout
	
	stats = battery_optimizer.get_battery_stats()
	_log_message("Battery: " + str(stats.battery_level * 100) + "% | Power Save: " + str(stats.power_save_active), "cyan")
	
	# Test critical battery
	_log_message("Testing critical battery level (5%)", "white")
	battery_optimizer.battery_level = 0.05
	battery_optimizer._check_battery_status()
	await get_tree().create_timer(1.0).timeout
	
	stats = battery_optimizer.get_battery_stats()
	_log_message("Battery: " + str(stats.battery_level * 100) + "% | Power Save: " + str(stats.power_save_active), "cyan")
	
	# Restore normal battery
	battery_optimizer.battery_level = 0.8
	battery_optimizer._check_battery_status()
	
	_log_message("Battery optimization testing completed!", "green")
	_test_completed()

func _test_ui_scaling():
	if test_running:
		return
	
	_log_message("=== TESTING UI SCALING ===", "yellow")
	test_running = true
	_set_buttons_enabled(false)
	
	status_label.text = "Testing UI scaling..."
	
	if UIScaler:
		var viewport_size = get_viewport().get_visible_rect().size
		_log_message("Viewport size: " + str(viewport_size), "white")
		
		var safe_margins = UIScaler.get_safe_area_margins()
		_log_message("Safe area margins: " + str(safe_margins), "white")
		
		var min_touch_size = UIScaler.get_minimum_touch_size()
		_log_message("Minimum touch size: " + str(min_touch_size), "white")
		
		var layout_mode = UIScaler.get_layout_mode()
		_log_message("Layout mode: " + layout_mode, "white")
		
		var is_tablet = UIScaler.is_tablet()
		_log_message("Is tablet: " + str(is_tablet), "white")
		
		_log_message("UI scaling test completed!", "green")
	else:
		_log_message("ERROR: UIScaler not available!", "red")
	
	_test_completed()

func _test_mobile_input():
	if test_running:
		return
	
	_log_message("=== TESTING MOBILE INPUT ===", "yellow")
	test_running = true
	_set_buttons_enabled(false)
	
	status_label.text = "Testing mobile input... (Touch the screen)"
	
	if MobileInputHandler:
		# Connect to input signals temporarily
		var touch_started_connection = MobileInputHandler.touch_started.connect(_on_touch_started)
		var touch_ended_connection = MobileInputHandler.touch_ended.connect(_on_touch_ended)
		var swipe_detected_connection = MobileInputHandler.swipe_detected.connect(_on_swipe_detected)
		var long_press_connection = MobileInputHandler.long_press_detected.connect(_on_long_press)
		
		_log_message("Touch the screen to test input handling...", "white")
		_log_message("Try: tap, swipe, long press", "cyan")
		
		# Wait for 10 seconds for user input
		await get_tree().create_timer(10.0).timeout
		
		# Disconnect signals
		MobileInputHandler.touch_started.disconnect(_on_touch_started)
		MobileInputHandler.touch_ended.disconnect(_on_touch_ended)
		MobileInputHandler.swipe_detected.disconnect(_on_swipe_detected)
		MobileInputHandler.long_press_detected.disconnect(_on_long_press)
		
		_log_message("Mobile input test completed!", "green")
	else:
		_log_message("ERROR: MobileInputHandler not available!", "red")
	
	_test_completed()

func _clear_asset_cache():
	if test_running:
		return
	
	_log_message("=== CLEARING ASSET CACHE ===", "yellow")
	
	if AssetManager:
		var cache_size = AssetManager.get_cache_size()
		_log_message("Cache size before clear: " + str(cache_size) + " assets", "white")
		
		AssetManager.clear_cache()
		
		cache_size = AssetManager.get_cache_size()
		_log_message("Cache size after clear: " + str(cache_size) + " assets", "white")
		_log_message("Asset cache cleared successfully!", "green")
	else:
		_log_message("ERROR: AssetManager not available!", "red")

func _back_to_menu():
	_log_message("Returning to main menu...", "white")
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

# Event handlers
func _on_asset_generated(asset_type: String, texture: ImageTexture):
	_log_message("Asset generated: " + asset_type, "green")

func _on_loading_progress(current: int, total: int, asset_type: String):
	var progress = float(current) / float(total) * 100.0
	progress_bar.value = progress
	status_label.text = "Generating assets... " + str(current) + "/" + str(total) + " (" + asset_type + ")"

func _on_all_assets_loaded():
	_log_message("All assets loaded successfully!", "green")
	_test_completed()

func _on_touch_started(position: Vector2):
	_log_message("Touch started at: " + str(position), "cyan")

func _on_touch_ended(position: Vector2):
	_log_message("Touch ended at: " + str(position), "cyan")

func _on_swipe_detected(direction: Vector2, strength: float):
	_log_message("Swipe detected - Direction: " + str(direction) + " Strength: " + str(strength), "cyan")

func _on_long_press(position: Vector2):
	_log_message("Long press detected at: " + str(position), "cyan")

# Helper functions
func _log_message(message: String, color: String = "white"):
	var timestamp = Time.get_datetime_string_from_system()
	var formatted_message = "[color=" + color + "][" + timestamp + "] " + message + "[/color]\n"
	log_text.text += formatted_message
	
	# Auto-scroll to bottom
	await get_tree().process_frame
	var scroll_container = log_text.get_parent()
	if scroll_container is ScrollContainer:
		scroll_container.scroll_vertical = scroll_container.get_v_scroll_bar().max_value
	
	print(message)

func _set_buttons_enabled(enabled: bool):
	test_asset_btn.disabled = not enabled
	test_performance_btn.disabled = not enabled
	test_battery_btn.disabled = not enabled
	test_ui_btn.disabled = not enabled
	test_input_btn.disabled = not enabled

func _test_completed():
	test_running = false
	_set_buttons_enabled(true)
	progress_bar.visible = false
	status_label.text = "Test completed. Ready for next test."
	_log_message("Test completed successfully!", "green")
