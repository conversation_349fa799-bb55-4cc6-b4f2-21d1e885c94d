extends Node

signal screen_size_changed(new_size: Vector2)

var base_resolution: Vector2 = Vector2(1080, 1920)  # Target mobile resolution
var current_scale: float = 1.0
var safe_area_margins: Vector4 = Vector4.ZERO  # top, right, bottom, left

func _ready():
	# Connect to window size changes
	get_tree().get_root().size_changed.connect(_on_screen_size_changed)
	
	# Initial setup
	_calculate_scale()
	_apply_safe_area()

func _on_screen_size_changed():
	_calculate_scale()
	_apply_safe_area()
	emit_signal("screen_size_changed", get_viewport().get_visible_rect().size)

func _calculate_scale():
	var viewport_size = get_viewport().get_visible_rect().size
	
	# Calculate scale based on the smaller dimension to ensure everything fits
	var scale_x = viewport_size.x / base_resolution.x
	var scale_y = viewport_size.y / base_resolution.y
	
	current_scale = min(scale_x, scale_y)
	
	print("Screen size: ", viewport_size, " Scale: ", current_scale)

func _apply_safe_area():
	# Get safe area for devices with notches
	if OS.has_feature("mobile"):
		var safe_area = DisplayServer.get_display_safe_area()
		var viewport_size = get_viewport().get_visible_rect().size
		
		# Calculate margins
		safe_area_margins.x = safe_area.position.y  # top
		safe_area_margins.y = viewport_size.x - (safe_area.position.x + safe_area.size.x)  # right
		safe_area_margins.z = viewport_size.y - (safe_area.position.y + safe_area.size.y)  # bottom
		safe_area_margins.w = safe_area.position.x  # left
		
		print("Safe area margins: ", safe_area_margins)

func get_scaled_size(original_size: Vector2) -> Vector2:
	"""Get size scaled for current screen"""
	return original_size * current_scale

func get_scaled_font_size(original_size: int) -> int:
	"""Get font size scaled for current screen"""
	return int(original_size * current_scale)

func get_safe_area_margins() -> Vector4:
	"""Get safe area margins (top, right, bottom, left)"""
	return safe_area_margins

func apply_safe_area_to_control(control: Control):
	"""Apply safe area margins to a control"""
	if control is MarginContainer:
		var margin_container = control as MarginContainer
		margin_container.add_theme_constant_override("margin_top", int(safe_area_margins.x))
		margin_container.add_theme_constant_override("margin_right", int(safe_area_margins.y))
		margin_container.add_theme_constant_override("margin_bottom", int(safe_area_margins.z))
		margin_container.add_theme_constant_override("margin_left", int(safe_area_margins.w))

func get_minimum_touch_size() -> Vector2:
	"""Get minimum touch target size (44dp in pixels)"""
	var dp_to_px = current_scale * (96.0 / 160.0)  # Android DP conversion
	return Vector2(44 * dp_to_px, 44 * dp_to_px)

func scale_ui_element(element: Control, base_size: Vector2):
	"""Scale a UI element based on current screen scale"""
	var scaled_size = get_scaled_size(base_size)
	var min_touch_size = get_minimum_touch_size()
	
	# Ensure minimum touch target size
	scaled_size.x = max(scaled_size.x, min_touch_size.x)
	scaled_size.y = max(scaled_size.y, min_touch_size.y)
	
	element.custom_minimum_size = scaled_size

func get_responsive_font_size(text_length: int, base_size: int) -> int:
	"""Get responsive font size based on text length and screen size"""
	var scaled_size = get_scaled_font_size(base_size)
	
	# Reduce font size for longer text
	if text_length > 20:
		scaled_size = int(scaled_size * 0.8)
	elif text_length > 15:
		scaled_size = int(scaled_size * 0.9)
	
	return max(scaled_size, 12)  # Minimum readable size

func is_tablet() -> bool:
	"""Check if device is likely a tablet based on screen size"""
	var viewport_size = get_viewport().get_visible_rect().size
	var diagonal_inches = sqrt(pow(viewport_size.x, 2) + pow(viewport_size.y, 2)) / (160 * current_scale)
	return diagonal_inches > 7.0  # Tablets are typically 7+ inches

func get_layout_mode() -> String:
	"""Get recommended layout mode based on screen size"""
	if is_tablet():
		return "tablet"
	else:
		var viewport_size = get_viewport().get_visible_rect().size
		if viewport_size.x > viewport_size.y:
			return "landscape"
		else:
			return "portrait"
