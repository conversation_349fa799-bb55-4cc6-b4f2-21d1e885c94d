#!/usr/bin/env python3
"""
Test Godot Scenario API integration
"""

import requests
import base64
import json
import time

# Same settings as in Godot
API_KEY = "api_StCDGknMcAZP7RKggd5rnfVE"
API_SECRET = "rJUJf3coKs1cbcxAxNWQ8ynf"
BASE_URL = "https://api.cloud.scenario.com/v1"

# Gothic art style prompts (same as in Godot)
ART_STYLE_BASE = "hand-drawn illustration, gothic cartoon style, Tim Burton aesthetic, dark fairy tale art, sketch-like lineart, atmospheric lighting, mobile game art, detailed textures"

ASSET_PROMPTS = {
    "main_background": ART_STYLE_BASE + ", ancient gothic castle silhouette, dark stormy sky, full moon behind clouds, spooky trees, mobile vertical composition, portrait orientation, misty atmosphere",
    "button_normal": ART_STYLE_BASE + ", gothic stone button, carved runes, moss details, weathered texture, game UI element, rectangular shape",
    "menu_frame": ART_STYLE_BASE + ", ornate gothic picture frame, carved stone details, ivy decoration, worn ancient texture, UI frame design, transparent center, medieval border"
}

def get_auth_header():
    credentials = base64.b64encode(f"{API_KEY}:{API_SECRET}".encode()).decode()
    return {"Authorization": f"Basic {credentials}"}

def test_gothic_asset_generation():
    """Test generating gothic assets like in Godot"""
    print("🎨 Testing Gothic Asset Generation (Godot Style)")
    print("=" * 60)
    
    headers = get_auth_header()
    headers["Content-Type"] = "application/json"
    
    # Test generating main background
    asset_type = "main_background"
    prompt = ASSET_PROMPTS[asset_type]
    
    payload = {
        "modelId": "flux.1-dev",
        "prompt": prompt,
        "negativePrompt": "blurry, low quality, modern elements, bright neon colors, 3D render, photograph, realistic, text, letters, words",
        "numSamples": 1,
        "guidance": 3.5,
        "numInferenceSteps": 20,
        "width": 1080,
        "height": 1920,
        "scheduler": "EulerAncestralDiscreteScheduler"
    }
    
    print(f"🖼️ Generating: {asset_type}")
    print(f"📝 Prompt: {prompt[:100]}...")
    print(f"📐 Size: {payload['width']}x{payload['height']}")
    
    try:
        response = requests.post(f"{BASE_URL}/generate/txt2img", 
                               headers=headers, 
                               json=payload,
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            job_id = data.get('job', {}).get('jobId')
            
            if job_id:
                print(f"✅ Job created: {job_id}")
                return poll_job_status(job_id, asset_type)
            else:
                print("❌ No job ID in response")
                return False
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def poll_job_status(job_id, asset_type):
    """Poll job status (same logic as Godot)"""
    print(f"⏳ Polling job status for {asset_type}...")
    
    headers = get_auth_header()
    max_attempts = 20
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{BASE_URL}/jobs/{job_id}", 
                                  headers=headers, 
                                  timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                job = data.get('job', {})
                status = job.get('status', 'unknown')
                progress = job.get('progress', 0.0)
                
                print(f"📊 Status: {status}, Progress: {progress*100:.1f}%")
                
                if status == "success":
                    print(f"🎉 {asset_type} generated successfully!")
                    
                    metadata = job.get('metadata', {})
                    asset_ids = metadata.get('assetIds', [])
                    
                    if asset_ids:
                        print(f"🖼️ Asset IDs: {asset_ids}")
                        return True
                    else:
                        print("❌ No asset IDs found")
                        return False
                
                elif status in ["failure", "canceled"]:
                    error = job.get('error', 'Unknown error')
                    print(f"❌ Job failed: {error}")
                    return False
                
                elif status in ["queued", "processing", "in-progress"]:
                    time.sleep(6)
                    continue
                
                else:
                    print(f"❓ Unknown status: {status}")
                    time.sleep(6)
                    continue
            
            else:
                print(f"❌ Polling error: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Polling exception: {e}")
            time.sleep(6)
            continue
    
    print("⏰ Job polling timeout")
    return False

def test_button_generation():
    """Test generating button asset"""
    print("\n🔘 Testing Button Generation...")
    
    headers = get_auth_header()
    headers["Content-Type"] = "application/json"
    
    payload = {
        "modelId": "flux.1-dev",
        "prompt": ASSET_PROMPTS["button_normal"],
        "negativePrompt": "blurry, low quality, modern elements, bright neon colors, 3D render, photograph, realistic",
        "numSamples": 1,
        "guidance": 3.5,
        "numInferenceSteps": 20,
        "width": 400,
        "height": 120,
        "scheduler": "EulerAncestralDiscreteScheduler"
    }
    
    print(f"🔘 Generating button (400x120)")
    
    try:
        response = requests.post(f"{BASE_URL}/generate/txt2img", 
                               headers=headers, 
                               json=payload,
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            job_id = data.get('job', {}).get('jobId')
            
            if job_id:
                print(f"✅ Button job created: {job_id}")
                return poll_job_status(job_id, "button_normal")
            else:
                print("❌ No job ID in response")
                return False
        else:
            print(f"❌ Button generation failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Button generation error: {e}")
        return False

def main():
    print("🎮 GODOT SCENARIO API INTEGRATION TEST")
    print("Testing the exact same configuration as in Godot project")
    print("=" * 70)
    
    # Test 1: Main background (portrait mobile)
    success1 = test_gothic_asset_generation()
    
    # Test 2: Button generation
    success2 = test_button_generation()
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS:")
    print(f"  🖼️ Background generation: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"  🔘 Button generation: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎯 ALL TESTS PASSED!")
        print("💡 Your Godot project should work correctly with Scenario API")
        print("🚀 Ready to run MainMenu.tscn in Godot!")
    else:
        print("\n❌ Some tests failed")
        print("🔧 Check the Godot ScenarioAPI.gd configuration")
    
    print("\n📋 Next steps:")
    print("1. Open the Godot project")
    print("2. Run MainMenu.tscn scene")
    print("3. Watch assets generate in real-time")
    print("4. Test on mobile device or emulator")

if __name__ == "__main__":
    main()
