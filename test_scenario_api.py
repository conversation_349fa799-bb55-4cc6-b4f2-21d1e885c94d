#!/usr/bin/env python3
"""
Test script pre Scenario API - preskúmanie možností generovania herných assetov
"""

import requests
import time
import base64
import json

# API kľúče
API_KEY = "api_StCDGknMcAZP7RKggd5rnfVE"
API_SECRET = "rJUJf3coKs1cbcxAxNWQ8ynf"

# Base URL pre Scenario API
BASE_URL = "https://api.cloud.scenario.com/v1"

# Vytvorenie autentifikačného headeru
def get_auth_header():
    credentials = base64.b64encode(f"{API_KEY}:{API_SECRET}".encode()).decode()
    return {"Authorization": f"Basic {credentials}"}

def test_api_connection():
    """Test základného pripojenia k API"""
    print("🔍 Testovanie pripojenia k Scenario API...")
    
    headers = get_auth_header()
    headers["accept"] = "application/json"
    
    try:
        # Test získania zoznamu modelov
        response = requests.get(f"{BASE_URL}/models", headers=headers)
        
        if response.status_code == 200:
            print("✅ API pripojenie úspešné!")
            data = response.json()
            print(f"📊 Počet dostupných modelov: {len(data.get('models', []))}")
            return True
        else:
            print(f"❌ Chyba API: {response.status_code}")
            print(f"Odpoveď: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba pripojenia: {e}")
        return False

def list_available_models():
    """Zobrazenie dostupných modelov"""
    print("\n🎨 Dostupné modely pre generovanie:")
    
    headers = get_auth_header()
    headers["accept"] = "application/json"
    
    try:
        response = requests.get(f"{BASE_URL}/models", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            
            print(f"\n📋 Zoznam modelov ({len(models)} celkom):")
            for i, model in enumerate(models[:10]):  # Zobraz prvých 10
                print(f"  {i+1}. {model.get('name', 'N/A')} (ID: {model.get('id', 'N/A')})")
                print(f"     Typ: {model.get('type', 'N/A')}")
                print(f"     Status: {model.get('status', 'N/A')}")
                print()
                
            if len(models) > 10:
                print(f"... a ďalších {len(models) - 10} modelov")
                
        else:
            print(f"❌ Chyba pri získavaní modelov: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Chyba: {e}")

def test_text_to_image():
    """Test generovania obrázka z textu"""
    print("\n🖼️ Test generovania obrázka z textu...")
    
    headers = get_auth_header()
    headers["Content-Type"] = "application/json"
    
    # Použijeme verejný model Flux
    payload = {
        "modelId": "flux.1-dev",
        "prompt": "fantasy medieval castle on a hill, detailed, game art style",
        "numSamples": 1,
        "guidance": 3.5,
        "numInferenceSteps": 20,
        "width": 512,
        "height": 512,
        "scheduler": "EulerAncestralDiscrete"
    }
    
    try:
        print("📤 Odosielanie požiadavky na generovanie...")
        response = requests.post(f"{BASE_URL}/generate/txt2img", 
                               headers=headers, 
                               json=payload)
        
        if response.status_code == 200:
            data = response.json()
            job_id = data.get('job', {}).get('jobId')
            
            if job_id:
                print(f"✅ Job vytvorený! ID: {job_id}")
                print("⏳ Čakanie na dokončenie...")
                
                # Polling pre výsledok
                return poll_job_status(job_id)
            else:
                print("❌ Chyba: Job ID nenájdené")
                return False
        else:
            print(f"❌ Chyba pri vytváraní jobu: {response.status_code}")
            print(f"Odpoveď: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

def poll_job_status(job_id):
    """Sledovanie stavu jobu"""
    headers = get_auth_header()
    
    status = ""
    attempts = 0
    max_attempts = 30  # Max 5 minút čakania
    
    while status not in ["success", "failure", "canceled"] and attempts < max_attempts:
        try:
            response = requests.get(f"{BASE_URL}/jobs/{job_id}", headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('job', {}).get('status', '')
                progress = data.get('job', {}).get('progress', 0)
                
                print(f"📊 Status: {status}, Progress: {progress*100:.1f}%")
                
                if status == "success":
                    asset_ids = data.get('job', {}).get('metadata', {}).get('assetIds', [])
                    print(f"🎉 Generovanie dokončené! Asset IDs: {asset_ids}")
                    return True
                elif status in ["failure", "canceled"]:
                    error = data.get('job', {}).get('error', 'Neznáma chyba')
                    print(f"❌ Job zlyhal: {error}")
                    return False
                    
            else:
                print(f"❌ Chyba pri kontrole stavu: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Chyba pri polling: {e}")
            return False
            
        attempts += 1
        time.sleep(10)  # Čakaj 10 sekúnd
    
    print("⏰ Timeout - job trvá príliš dlho")
    return False

def explore_api_capabilities():
    """Preskúmanie možností API"""
    print("\n🔍 Preskúmanie možností Scenario API:")
    
    capabilities = [
        "✨ Text-to-Image generovanie",
        "🖼️ Image-to-Image transformácie", 
        "🎬 Video generovanie",
        "🗿 3D model generovanie",
        "🎨 Custom model trénovanie",
        "🔄 Batch processing",
        "🎭 ControlNet modality",
        "🌅 Skybox generovanie",
        "📐 Image upscaling",
        "✂️ Background removal",
        "🎯 Object detection",
        "🖌️ Inpainting/Outpainting"
    ]
    
    for capability in capabilities:
        print(f"  {capability}")

def main():
    """Hlavná funkcia"""
    print("🎮 SCENARIO API EXPLORER")
    print("=" * 50)
    
    # Test pripojenia
    if not test_api_connection():
        print("❌ Nemožno pokračovať bez funkčného API pripojenia")
        return
    
    # Preskúmanie možností
    explore_api_capabilities()
    
    # Zoznam modelov
    list_available_models()
    
    # Test generovania
    print("\n" + "=" * 50)
    user_input = input("Chcete otestovať generovanie obrázka? (y/n): ")
    
    if user_input.lower() in ['y', 'yes', 'ano', 'a']:
        test_text_to_image()
    
    print("\n🎯 Scenario API je pripravené na použitie vo vašej hre!")
    print("💡 Môžete generovať:")
    print("   - Herné postavy a objekty")
    print("   - Textúry a pozadia") 
    print("   - Koncepty a artwork")
    print("   - 3D modely")
    print("   - Animácie a videá")

if __name__ == "__main__":
    main()
