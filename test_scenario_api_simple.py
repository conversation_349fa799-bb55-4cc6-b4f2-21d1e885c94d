#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON><PERSON><PERSON> test Scenario API - overenie funkcionality
"""

import requests
import base64
import json
import time

# API credentials
API_KEY = "api_StCDGknMcAZP7RKggd5rnfVE"
API_SECRET = "rJUJf3coKs1cbcxAxNWQ8ynf"
BASE_URL = "https://api.cloud.scenario.com/v1"

def get_auth_header():
    credentials = base64.b64encode(f"{API_KEY}:{API_SECRET}".encode()).decode()
    return {"Authorization": f"Basic {credentials}"}

def test_api_connection():
    """Test basic API connection"""
    print("🔍 Testing Scenario API connection...")

    headers = get_auth_header()
    headers["accept"] = "application/json"

    try:
        response = requests.get(f"{BASE_URL}/models", headers=headers, timeout=10)

        if response.status_code == 200:
            print("✅ API connection successful!")
            data = response.json()
            models = data.get('models', [])
            print(f"📊 Available models: {len(models)}")

            # Show all models with status
            for i, model in enumerate(models):
                name = model.get('name', 'N/A')
                model_id = model.get('id', 'N/A')
                status = model.get('status', 'N/A')
                print(f"  {i+1}. {name} (ID: {model_id}) - Status: {status}")

            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def get_public_models():
    """Get public models"""
    print("\n🌐 Checking for public models...")

    headers = get_auth_header()
    headers["accept"] = "application/json"

    # Try to get public models
    try:
        response = requests.get(f"{BASE_URL}/models/public", headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"📊 Public models: {len(models)}")

            for i, model in enumerate(models[:5]):
                name = model.get('name', 'N/A')
                model_id = model.get('id', 'N/A')
                print(f"  {i+1}. {name} (ID: {model_id})")

            return models
        else:
            print(f"ℹ️ Public models not accessible: {response.status_code}")
            return []

    except Exception as e:
        print(f"❌ Error getting public models: {e}")
        return []

def test_simple_generation():
    """Test simple image generation"""
    print("\n🎨 Testing simple image generation...")
    
    headers = get_auth_header()
    headers["Content-Type"] = "application/json"
    
    # Simple test prompt using public Flux model
    payload = {
        "modelId": "flux.1-dev",
        "prompt": "simple gothic castle, dark sky, cartoon style",
        "numSamples": 1,
        "guidance": 3.5,
        "numInferenceSteps": 20,
        "width": 512,
        "height": 512,
        "scheduler": "EulerAncestralDiscreteScheduler"
    }

    try:
        print("📤 Sending generation request...")
        # Use standard txt2img endpoint
        response = requests.post(f"{BASE_URL}/generate/txt2img",
                               headers=headers,
                               json=payload,
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            # Check if we got a job ID
            if 'job' in data and 'jobId' in data['job']:
                job_id = data['job']['jobId']
                print(f"✅ Job created successfully! ID: {job_id}")
                
                # Poll job status
                return poll_job_status(job_id)
            
            # Check if we got direct images
            elif 'images' in data and len(data['images']) > 0:
                print("✅ Image generated directly!")
                image_url = data['images'][0]['url']
                print(f"🖼️ Image URL: {image_url}")
                return True
            
            else:
                print("❌ Unexpected response format")
                print(f"Response: {json.dumps(data, indent=2)}")
                return False
        
        else:
            print(f"❌ Generation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return False

def poll_job_status(job_id):
    """Poll job status until completion"""
    print(f"⏳ Polling job status: {job_id}")
    
    headers = get_auth_header()
    max_attempts = 20  # 2 minutes max
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{BASE_URL}/jobs/{job_id}", 
                                  headers=headers, 
                                  timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                job = data.get('job', {})
                status = job.get('status', 'unknown')
                progress = job.get('progress', 0.0)
                
                print(f"📊 Status: {status}, Progress: {progress*100:.1f}%")
                
                if status == "success":
                    print("🎉 Job completed successfully!")
                    
                    # Get asset IDs
                    metadata = job.get('metadata', {})
                    asset_ids = metadata.get('assetIds', [])
                    
                    if asset_ids:
                        print(f"🖼️ Generated asset IDs: {asset_ids}")
                        return True
                    else:
                        print("❌ No asset IDs found")
                        return False
                
                elif status in ["failure", "canceled"]:
                    error = job.get('error', 'Unknown error')
                    print(f"❌ Job failed: {error}")
                    return False
                
                elif status in ["queued", "processing"]:
                    # Continue polling
                    time.sleep(6)  # Wait 6 seconds
                    continue
                
                else:
                    print(f"❓ Unknown status: {status}")
                    time.sleep(6)
                    continue
            
            else:
                print(f"❌ Polling error: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Polling exception: {e}")
            time.sleep(6)
            continue
    
    print("⏰ Job polling timeout")
    return False

def main():
    print("🎮 SCENARIO API - SIMPLE TEST")
    print("=" * 40)

    # Test connection
    if not test_api_connection():
        print("\n❌ Cannot proceed without API connection")
        return

    # Check public models
    public_models = get_public_models()

    # Test generation
    print("\n" + "=" * 40)
    success = test_simple_generation()

    print("\n" + "=" * 40)
    if success:
        print("🎯 All tests passed! API is working correctly.")
        print("💡 You can now use the API in your Godot project.")
    else:
        print("❌ Some tests failed. Check your API configuration.")
        print("🔧 Troubleshooting:")
        print("  - Your custom model needs to be trained first")
        print("  - Try using a public model like flux.1-dev")
        print("  - Check Scenario documentation for model training")
        print("  - Verify API keys are correct")
        print("  - Check internet connection")

if __name__ == "__main__":
    main()
